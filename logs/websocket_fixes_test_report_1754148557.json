{"results": {"total": 10, "passed": 1, "failed": 9, "skipped": 0, "coverage": 10.0, "start_time": 1754148557.9242187, "end_time": 1754148557.9646392, "execution_time": 0.0404205322265625}, "test_cases": [{"name": "OKX_HTTP_503_HANDLING", "description": "验证OKX HTTP 503错误检测和备用端点切换", "result": "FAIL", "execution_time": 0.037339210510253906, "error_message": "cannot import name 'OKXWebSocket' from 'websocket.okx_ws' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/okx_ws.py)", "details": null}, {"name": "GATE_RATE_LIMIT_DETECTION", "description": "验证Gate.io限速检测和自动恢复机制", "result": "FAIL", "execution_time": 7.867813110351562e-06, "error_message": "cannot import name 'GateWebSocket' from 'websocket.gate_ws' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/gate_ws.py)", "details": null}, {"name": "HEARTBEAT_LOGGER_FUNCTIONALITY", "description": "验证专用心跳日志记录器的完整功能", "result": "FAIL", "execution_time": 0.0008382797241210938, "error_message": "log_http_503_error() missing 1 required positional argument: 'retry_count'", "details": null}, {"name": "UNIFIED_MODULES_USAGE", "description": "验证所有修复都使用了统一模块，没有造轮子", "result": "FAIL", "execution_time": 1.0251998901367188e-05, "error_message": "cannot import name 'get_unified_connection_pool_manager' from 'websocket.unified_connection_pool_manager' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/unified_connection_pool_manager.py)", "details": null}, {"name": "THREE_EXCHANGE_HEARTBEAT_SYNC", "description": "验证OKX、Gate.io、Bybit心跳机制同步一致性", "result": "FAIL", "execution_time": 6.9141387939453125e-06, "error_message": "cannot import name 'OKXWebSocket' from 'websocket.okx_ws' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/okx_ws.py)", "details": null}, {"name": "CONNECTION_POOL_INTEGRATION", "description": "验证WebSocket修复与统一连接池管理器的集成", "result": "FAIL", "execution_time": 5.245208740234375e-06, "error_message": "cannot import name 'get_unified_connection_pool_manager' from 'websocket.unified_connection_pool_manager' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/unified_connection_pool_manager.py)", "details": null}, {"name": "ERROR_RECOVERY_CASCADE", "description": "验证错误恢复机制的级联效应和一致性", "result": "PASS", "execution_time": 0.0013535022735595703, "error_message": "", "details": {"error_types_verified": 2, "handler_methods": 2}}, {"name": "NETWORK_FLUCTUATION_SIMULATION", "description": "模拟网络波动情况下的WebSocket稳定性", "result": "FAIL", "execution_time": 9.775161743164062e-06, "error_message": "cannot import name 'GateWebSocket' from 'websocket.gate_ws' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/gate_ws.py)", "details": null}, {"name": "HIGH_CONCURRENCY_PRESSURE", "description": "验证高并发情况下的WebSocket性能", "result": "FAIL", "execution_time": 4.0531158447265625e-06, "error_message": "cannot import name 'GateWebSocket' from 'websocket.gate_ws' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/gate_ws.py)", "details": null}, {"name": "LONG_TERM_STABILITY", "description": "验证长期运行的稳定性配置", "result": "FAIL", "execution_time": 9.5367431640625e-06, "error_message": "cannot import name 'GateWebSocket' from 'websocket.gate_ws' (/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/gate_ws.py)", "details": null}]}