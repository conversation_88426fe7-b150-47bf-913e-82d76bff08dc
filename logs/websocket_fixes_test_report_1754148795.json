{"results": {"total": 10, "passed": 9, "failed": 1, "skipped": 0, "coverage": 90.0, "start_time": 1754148795.755584, "end_time": 1754148795.7853506, "execution_time": 0.029766559600830078}, "test_cases": [{"name": "OKX_HTTP_503_HANDLING", "description": "验证OKX HTTP 503错误检测和备用端点切换", "result": "PASS", "execution_time": 0.026113271713256836, "error_message": "", "details": {"backup_endpoints": 2, "primary_url": "wss://ws.okx.com:8443/ws/v5/public", "backup_urls": ["wss://wsaws.okx.com:8443/ws/v5/public", "wss://ws.okx.com/ws/v5/public"]}}, {"name": "GATE_RATE_LIMIT_DETECTION", "description": "验证Gate.io限速检测和自动恢复机制", "result": "PASS", "execution_time": 9.608268737792969e-05, "error_message": "", "details": {"rate_limit_detection": true, "heartbeat_interval": 10, "connection_timeout": 15, "rate_limit_backoff": 30.0}}, {"name": "HEARTBEAT_LOGGER_FUNCTIONALITY", "description": "验证专用心跳日志记录器的完整功能", "result": "PASS", "execution_time": 0.0010461807250976562, "error_message": "", "details": {"log_functions_tested": 4, "test_time": 1754148795.7823615}}, {"name": "UNIFIED_MODULES_USAGE", "description": "验证所有修复都使用了统一模块，没有造轮子", "result": "PASS", "execution_time": 0.0005359649658203125, "error_message": "", "details": {"unified_modules_verified": 3, "connection_pool": "✅", "timestamp_processor": "✅", "error_handler": "✅"}}, {"name": "THREE_EXCHANGE_HEARTBEAT_SYNC", "description": "验证OKX、Gate.io、Bybit心跳机制同步一致性", "result": "PASS", "execution_time": 0.00030612945556640625, "error_message": "", "details": {"heartbeat_intervals": {"okx": 15, "gate": 10, "bybit": 15}, "exchanges_tested": 3}}, {"name": "CONNECTION_POOL_INTEGRATION", "description": "验证WebSocket修复与统一连接池管理器的集成", "result": "FAIL", "execution_time": 8.821487426757812e-06, "error_message": "缺少连接错误处理方法", "details": null}, {"name": "ERROR_RECOVERY_CASCADE", "description": "验证错误恢复机制的级联效应和一致性", "result": "PASS", "execution_time": 2.6702880859375e-05, "error_message": "", "details": {"error_types_verified": 2, "handler_methods": 2}}, {"name": "NETWORK_FLUCTUATION_SIMULATION", "description": "模拟网络波动情况下的WebSocket稳定性", "result": "PASS", "execution_time": 7.772445678710938e-05, "error_message": "", "details": {"timeout_ratio": 1.5, "connection_timeout": 15, "heartbeat_interval": 10}}, {"name": "HIGH_CONCURRENCY_PRESSURE", "description": "验证高并发情况下的WebSocket性能", "result": "PASS", "execution_time": 0.0006704330444335938, "error_message": "", "details": {"instances_created": 10, "instance_types": 2}}, {"name": "LONG_TERM_STABILITY", "description": "验证长期运行的稳定性配置", "result": "PASS", "execution_time": 6.103515625e-05, "error_message": "", "details": {"rate_limit_backoff": 30.0, "max_consecutive_failures": 3}}]}