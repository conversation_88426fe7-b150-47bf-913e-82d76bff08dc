#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 WebSocket时间戳问题精确诊断脚本
根据用户提示和日志分析，精准定位时间戳不一致问题的根本原因
"""

import time
import json
import logging
from decimal import Decimal
from typing import Dict, Any, Optional, List
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('diagnostic_results/websocket_timestamp_diagnosis.log')
        ]
    )
    return logging.getLogger(__name__)

def analyze_timestamp_issue():
    """分析时间戳问题的根本原因"""
    logger = setup_logging()
    
    print("🔥 WebSocket时间戳问题精确诊断")
    print("=" * 60)
    
    # 1. 问题概述
    print("\n📋 问题概述:")
    print("- Gate.io和OKX出现大量时间戳新鲜度检查失败")
    print("- 时间戳年龄: 18-36秒，远超1秒阈值")
    print("- Bybit没有报告相同错误")
    print("- WebSocket数据流阻塞30秒以上")
    
    # 2. 根本原因分析
    print("\n🔍 根本原因分析:")
    
    # 2.1 时间戳处理差异
    print("\n2.1 时间戳处理方式差异:")
    timestamp_processing = {
        "Gate.io": {
            "方式": "processor.get_synced_timestamp(data) - 实例方法",
            "位置": "gate_ws.py:333",
            "数据新鲜度检查": "✅ 启用 (_extract_server_timestamp_for_monitoring)",
            "阈值": "1000ms"
        },
        "OKX": {
            "方式": "self.timestamp_processor.get_synced_timestamp(book) - 实例方法", 
            "位置": "okx_ws.py:44-45 (初始化时创建处理器)",
            "数据新鲜度检查": "✅ 启用 (_extract_server_timestamp_for_monitoring)",
            "阈值": "1000ms"
        },
        "Bybit": {
            "方式": "get_synced_timestamp('bybit', data) - 全局函数",
            "位置": "bybit_ws.py:459", 
            "数据新鲜度检查": "❌ 未启用 (使用全局函数，绕过实例检查)",
            "阈值": "N/A"
        }
    }
    
    for exchange, details in timestamp_processing.items():
        print(f"\n{exchange}:")
        for key, value in details.items():
            print(f"  {key}: {value}")
    
    # 2.2 关键发现
    print("\n🔥 关键发现:")
    print("1. Bybit使用全局函数 get_synced_timestamp() 调用")
    print("2. Gate.io和OKX使用实例方法 processor.get_synced_timestamp() 调用")
    print("3. 实例方法会触发 _extract_server_timestamp_for_monitoring() 检查")
    print("4. 全局函数绕过了数据新鲜度检查，因此Bybit不报错")
    
    # 3. 时间戳计算错误分析
    print("\n⚠️ 时间戳计算错误分析:")
    
    # 模拟实际日志中的时间戳
    current_time = 1754142127000  # 模拟当前时间 (毫秒)
    gate_timestamp = 1754142095794  # Gate.io实际时间戳
    okx_timestamp = 1754142093005   # OKX实际时间戳
    
    gate_age = current_time - gate_timestamp
    okx_age = current_time - okx_timestamp
    
    print(f"当前时间: {current_time}")
    print(f"Gate.io时间戳: {gate_timestamp}")
    print(f"OKX时间戳: {okx_timestamp}")
    print(f"Gate.io数据年龄: {gate_age}ms ({gate_age/1000:.1f}秒)")
    print(f"OKX数据年龄: {okx_age}ms ({okx_age/1000:.1f}秒)")
    print(f"阈值: 1000ms (1秒)")
    print(f"Gate.io超期: {gate_age > 1000} (超期{(gate_age-1000)/1000:.1f}秒)")
    print(f"OKX超期: {okx_age > 1000} (超期{(okx_age-1000)/1000:.1f}秒)")
    
    # 4. WebSocket数据流阻塞分析
    print("\n🚨 WebSocket数据流阻塞分析:")
    silent_start = 1754142085906  # 最后数据更新时间
    current_check = 1754142116263  # 检查时间
    silent_duration = (current_check - silent_start) / 1000
    
    print(f"最后数据更新时间: {silent_start}")
    print(f"检查时间: {current_check}")
    print(f"静默持续时间: {silent_duration:.1f}秒")
    print(f"阻塞阈值: 30秒")
    print(f"是否阻塞: {silent_duration > 30} ({'是' if silent_duration > 30 else '否'})")
    
    # 5. 📋 内部检查清单
    print("\n📋 内部检查清单验证:")
    checklist = {
        "1. 现有架构中是否已有此功能": "✅ 有 - 统一时间戳处理器(unified_timestamp_processor.py)",
        "2. 是否应该在统一模块中实现": "✅ 是 - 三个交易所应使用相同的时间戳处理方式",
        "3. 问题的根本原因": "🔥 Bybit使用全局函数绕过了数据新鲜度检查",
        "4. 检查链路和接口的结果": "🔥 发现Bybit接口调用不一致，存在链路中断",
        "5. 其他两个交易所是否有同样问题": "❌ 否 - Gate.io和OKX都正确使用实例方法",
        "6. 如何从源头最优解决问题": "🔥 统一三个交易所的时间戳处理调用方式",
        "7. 是否重复调用，存在造轮子": "❌ 否 - 使用了统一模块",
        "8. 横向深度全面查阅资料并思考": "✅ 已查阅07文档，确认是通用多代币期货溢价套利系统"
    }
    
    for question, answer in checklist.items():
        print(f"{question}: {answer}")
    
    # 6. 修复方案
    print("\n🔧 精准修复方案:")
    print("1. 修复Bybit WebSocket时间戳处理调用")
    print("   - 位置: bybit_ws.py:459")
    print("   - 修复前: get_synced_timestamp('bybit', data)")
    print("   - 修复后: self.timestamp_processor.get_synced_timestamp(data)")
    print("   - 添加实例初始化: self.timestamp_processor = get_timestamp_processor('bybit')")
    
    print("\n2. 优化OKX WebSocket数据流阻塞检测")
    print("   - 加强pong响应处理")
    print("   - 增加30秒数据流阻塞自动重连")
    
    print("\n3. 统一三个交易所的时间戳处理阈值")
    print("   - 确保所有交易所使用相同的1000ms阈值")
    print("   - 删除任何特殊化处理")
    
    # 7. 验证结果预期
    print("\n✅ 修复后预期结果:")
    print("1. Bybit也会报告时间戳新鲜度检查失败（与Gate.io/OKX一致）")
    print("2. 三个交易所的时间戳处理完全统一")
    print("3. WebSocket数据流阻塞能及时检测和恢复")
    print("4. 时间戳不同步错误将统一标准处理")
    
    logger.info("WebSocket时间戳问题诊断完成")
    return True

def diagnose_specific_timestamp_errors():
    """诊断具体的时间戳错误"""
    print("\n🎯 具体时间戳错误诊断:")
    
    # 从日志中提取的实际错误
    errors = [
        {
            "exchange": "gate",
            "timestamp_age_ms": 34995,
            "max_age_ms": 1000,
            "discarded_timestamp": 1754142095794,
            "log_time": "2025-08-02 15:42:10,789"
        },
        {
            "exchange": "okx", 
            "timestamp_age_ms": 37786,
            "max_age_ms": 1000,
            "discarded_timestamp": 1754142093005,
            "log_time": "2025-08-02 15:42:10,791"
        }
    ]
    
    print("实际日志错误分析:")
    for error in errors:
        print(f"\n{error['exchange'].upper()}:")
        print(f"  时间戳年龄: {error['timestamp_age_ms']}ms ({error['timestamp_age_ms']/1000:.1f}秒)")
        print(f"  阈值: {error['max_age_ms']}ms")
        print(f"  超期倍数: {error['timestamp_age_ms']/error['max_age_ms']:.1f}x")
        print(f"  丢弃时间戳: {error['discarded_timestamp']}")
        print(f"  日志时间: {error['log_time']}")
    
    print("\n结论: Gate.io和OKX的数据确实严重超期，需要解决根本的时间戳同步问题")

if __name__ == "__main__":
    try:
        # 确保诊断结果目录存在
        os.makedirs('diagnostic_results', exist_ok=True)
        
        # 执行诊断
        analyze_timestamp_issue()
        diagnose_specific_timestamp_errors()
        
        print("\n🎉 诊断完成！")
        print("诊断报告已保存到: diagnostic_results/websocket_timestamp_diagnosis.log")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()