#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 WebSocket时间戳修复验证脚本
验证Bybit时间戳处理修复是否成功，确保三个交易所统一
"""

import sys
import os
import importlib.util
import inspect

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
websocket_path = os.path.join(project_root, '123')
sys.path.insert(0, websocket_path)

def verify_bybit_timestamp_fix():
    """验证Bybit时间戳处理修复"""
    print("🔥 WebSocket时间戳修复验证")
    print("=" * 50)
    
    try:
        # 导入Bybit WebSocket客户端
        from websocket.bybit_ws import BybitWebSocketClient
        
        print("✅ Bybit WebSocket客户端导入成功")
        
        # 创建实例检查
        client = BybitWebSocketClient("spot")
        
        # 检查是否有timestamp_processor属性
        if hasattr(client, 'timestamp_processor'):
            print("✅ Bybit现在有timestamp_processor实例属性")
            
            # 检查timestamp_processor类型
            processor_type = type(client.timestamp_processor).__name__
            print(f"✅ 时间戳处理器类型: {processor_type}")
            
            # 检查是否有get_synced_timestamp方法
            if hasattr(client.timestamp_processor, 'get_synced_timestamp'):
                print("✅ 时间戳处理器有get_synced_timestamp实例方法")
            else:
                print("❌ 时间戳处理器缺少get_synced_timestamp实例方法")
                
        else:
            print("❌ Bybit缺少timestamp_processor实例属性")
            
        # 读取源代码检查修复情况
        import websocket.bybit_ws as bybit_module
        source_lines = inspect.getsource(bybit_module.BybitWebSocketClient)
        
        # 检查关键修复点
        fixes_found = {
            "实例化timestamp_processor": "self.timestamp_processor = get_timestamp_processor" in source_lines,
            "使用实例方法调用": "self.timestamp_processor.get_synced_timestamp" in source_lines,
            "移除全局函数调用": 'get_synced_timestamp("bybit"' not in source_lines
        }
        
        print("\n🔍 代码修复验证:")
        for fix_name, is_fixed in fixes_found.items():
            status = "✅" if is_fixed else "❌"
            print(f"{status} {fix_name}: {'已修复' if is_fixed else '未修复'}")
            
        # 与其他交易所对比
        print("\n🔄 与其他交易所对比:")
        
        # Gate.io检查
        try:
            from websocket.gate_ws import GateWebSocketClient
            gate_client = GateWebSocketClient("spot")
            gate_has_processor = hasattr(gate_client, 'timestamp_processor')
            print(f"Gate.io timestamp_processor: {'✅ 有' if gate_has_processor else '❌ 无'}")
        except Exception as e:
            print(f"Gate.io检查失败: {e}")
            
        # OKX检查  
        try:
            from websocket.okx_ws import OKXWebSocketClient
            okx_client = OKXWebSocketClient("spot")
            okx_has_processor = hasattr(okx_client, 'timestamp_processor')
            print(f"OKX timestamp_processor: {'✅ 有' if okx_has_processor else '❌ 无'}")
        except Exception as e:
            print(f"OKX检查失败: {e}")
            
        # 总体评估
        all_fixes_applied = all(fixes_found.values())
        print(f"\n🎯 总体修复状态: {'✅ 完成' if all_fixes_applied else '❌ 未完成'}")
        
        if all_fixes_applied:
            print("🎉 Bybit时间戳处理已成功修复，现在与Gate.io和OKX保持一致")
            print("🔍 预期结果: Bybit也会开始报告时间戳新鲜度检查失败错误")
        else:
            print("⚠️ 修复未完全完成，请检查代码")
            
        return all_fixes_applied
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_timestamp_consistency():
    """检查时间戳处理一致性"""
    print("\n🔍 时间戳处理一致性检查:")
    
    exchanges = ["gate", "bybit", "okx"]
    consistency_results = {}
    
    for exchange in exchanges:
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            processor = get_timestamp_processor(exchange)
            
            # 检查处理器属性
            consistency_results[exchange] = {
                "exchange_name": processor.exchange_name,
                "has_extract_method": hasattr(processor, '_extract_server_timestamp_for_monitoring'),
                "config_available": hasattr(processor, 'config'),
                "sync_status": processor.time_synced
            }
            
        except Exception as e:
            consistency_results[exchange] = {"error": str(e)}
    
    print("处理器配置一致性:")
    for exchange, results in consistency_results.items():
        print(f"\n{exchange.upper()}:")
        if "error" in results:
            print(f"  ❌ 错误: {results['error']}")
        else:
            for key, value in results.items():
                print(f"  {key}: {value}")
    
    return consistency_results

if __name__ == "__main__":
    try:
        # 验证修复
        fix_success = verify_bybit_timestamp_fix()
        
        # 检查一致性
        consistency_results = check_timestamp_consistency()
        
        if fix_success:
            print("\n🎉 修复验证成功！")
            print("现在三个交易所的时间戳处理已统一")
        else:
            print("\n❌ 修复验证失败！")
            
    except Exception as e:
        print(f"❌ 验证脚本执行失败: {e}")
        import traceback
        traceback.print_exc()