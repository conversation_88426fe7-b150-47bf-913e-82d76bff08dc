"""
🔥 统一HTTP会话管理器 - 第19个核心统一模块
解决aiohttp会话资源泄漏问题，确保所有HTTP会话正确管理和清理

职责：
1. 统一管理所有交易所的HTTP会话
2. 确保会话正确创建、使用和关闭
3. 提供异步上下文管理器支持
4. 统一的异常处理和资源清理
5. 防止会话泄漏和重复创建

设计原则：
- 遵循28个核心统一模块的设计标准
- 单例模式，全局唯一实例
- 线程安全的会话管理
- 优雅的资源清理机制
"""

import asyncio
import aiohttp
import logging
import time
import weakref
import ssl
import os
from typing import Dict, Optional, Set, Any, Union, List
from contextlib import asynccontextmanager

from utils.logger import get_logger
# 🔥 网络配置管理器集成
from config.network_config import get_network_config_manager

class UnifiedHttpSessionManager:
    """统一HTTP会话管理器 - 🔥 第19个核心统一模块"""

    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化统一HTTP会话管理器"""
        if hasattr(self, '_initialized'):
            return

        self.logger = get_logger(self.__class__.__name__)

        # 会话存储：exchange_name -> session
        self._sessions: Dict[str, aiohttp.ClientSession] = {}

        # 会话创建时间跟踪
        self._session_created_time: Dict[str, float] = {}

        # 活跃会话引用跟踪（用于调试）
        self._active_sessions: Set[weakref.ref] = set()

        # 管理器状态
        self._is_closing = False
        self._cleanup_task: Optional[asyncio.Task] = None

        # 统计信息
        self._total_sessions_created = 0
        self._total_sessions_closed = 0

        # 🔥 新增：REST API多端点配置
        self._api_endpoints = self._init_api_endpoints()
        self._endpoint_failures: Dict[str, Dict[str, int]] = {}  # exchange -> {endpoint: failure_count}
        self._endpoint_last_success: Dict[str, Dict[str, float]] = {}  # exchange -> {endpoint: timestamp}

        self._initialized = True
        self.logger.info("✅ 统一HTTP会话管理器初始化完成")

    def _init_api_endpoints(self) -> Dict[str, List[Dict[str, Any]]]:
        """🔥 新增：初始化REST API多端点配置"""
        return {
            "gate": [
                {
                    "spot": "https://api.gateio.ws/api/v4",
                    "futures": "https://api.gateio.ws/api/v4/futures/usdt",
                    "priority": 1,
                    "region": "global",
                    "is_backup": False
                },
                {
                    "spot": "https://fx-api.gateio.ws/api/v4",
                    "futures": "https://fx-api.gateio.ws/api/v4/futures/usdt",
                    "priority": 2,
                    "region": "asia",
                    "is_backup": True
                }
            ],
            "bybit": [
                {
                    "spot": "https://api.bybit.com",
                    "futures": "https://api.bybit.com",
                    "priority": 1,
                    "region": "global",
                    "is_backup": False
                },
                {
                    "spot": "https://api.bytick.com",
                    "futures": "https://api.bytick.com",
                    "priority": 2,
                    "region": "backup",
                    "is_backup": True
                }
            ],
            "okx": [
                {
                    "spot": "https://www.okx.com",
                    "futures": "https://www.okx.com",
                    "priority": 1,
                    "region": "global",
                    "is_backup": False
                },
                {
                    "spot": "https://aws.okx.com",
                    "futures": "https://aws.okx.com",
                    "priority": 2,
                    "region": "aws",
                    "is_backup": True
                }
            ]
        }

    def _select_best_api_endpoint(self, exchange_name: str, market_type: str = "spot",
                                 failed_endpoint: Optional[str] = None) -> Optional[str]:
        """🔥 新增：选择最佳REST API端点"""
        endpoints = self._api_endpoints.get(exchange_name, [])
        if not endpoints:
            self.logger.error(f"❌ 未配置REST API端点: {exchange_name}")
            return None

        # 排除失败的端点
        available_endpoints = []
        for endpoint_config in endpoints:
            endpoint_url = endpoint_config.get(market_type, endpoint_config.get("spot"))
            if endpoint_url and endpoint_url != failed_endpoint:
                available_endpoints.append((endpoint_url, endpoint_config))

        if not available_endpoints:
            self.logger.error(f"❌ 无可用REST API端点: {exchange_name} {market_type}")
            return None

        # 优先选择非备用端点
        primary_endpoints = [(url, config) for url, config in available_endpoints if not config.get("is_backup", False)]
        if primary_endpoints:
            # 按优先级选择
            best_endpoint = min(primary_endpoints, key=lambda x: x[1]["priority"])
            self.logger.info(f"✅ 选择主REST API端点: {exchange_name} -> {best_endpoint[0]}")
            return best_endpoint[0]

        # 选择最佳备用端点
        best_backup = min(available_endpoints, key=lambda x: x[1]["priority"])
        self.logger.warning(f"⚠️ 使用备用REST API端点: {exchange_name} -> {best_backup[0]}")
        return best_backup[0]

    def _record_endpoint_failure(self, exchange_name: str, endpoint: str):
        """🔥 新增：记录端点失败"""
        if exchange_name not in self._endpoint_failures:
            self._endpoint_failures[exchange_name] = {}

        self._endpoint_failures[exchange_name][endpoint] = self._endpoint_failures[exchange_name].get(endpoint, 0) + 1
        self.logger.warning(f"⚠️ REST API端点失败: {exchange_name} {endpoint} (失败次数: {self._endpoint_failures[exchange_name][endpoint]})")

    def _record_endpoint_success(self, exchange_name: str, endpoint: str):
        """🔥 新增：记录端点成功"""
        if exchange_name not in self._endpoint_last_success:
            self._endpoint_last_success[exchange_name] = {}

        self._endpoint_last_success[exchange_name][endpoint] = time.time()
        # 重置失败计数
        if exchange_name in self._endpoint_failures and endpoint in self._endpoint_failures[exchange_name]:
            self._endpoint_failures[exchange_name][endpoint] = 0

    def _create_ssl_context(self) -> ssl.SSLContext:
        """
        创建SSL上下文 - 🔥 智能SSL配置

        支持环境变量控制：
        - SSL_VERIFY=false: 禁用SSL验证（开发/测试环境）
        - SSL_VERIFY=true: 启用SSL验证（生产环境，默认）
        """
        ssl_context = ssl.create_default_context()

        # 检查环境变量
        ssl_verify = os.getenv('SSL_VERIFY', 'true').lower()

        if ssl_verify in ('false', '0', 'no', 'off'):
            # 开发/测试环境：禁用SSL验证
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            self.logger.warning("⚠️ SSL验证已禁用 - 仅适用于开发/测试环境")
        else:
            # 生产环境：启用SSL验证
            ssl_context.check_hostname = True
            ssl_context.verify_mode = ssl.CERT_REQUIRED
            self.logger.info("✅ SSL验证已启用 - 生产环境安全配置")

        return ssl_context
    
    async def get_session(self, exchange_name: str) -> aiohttp.ClientSession:
        """
        获取指定交易所的HTTP会话
        
        Args:
            exchange_name: 交易所名称 (gate, bybit, okx)
            
        Returns:
            aiohttp.ClientSession: HTTP会话实例
        """
        async with self._lock:
            # 检查是否正在关闭
            if self._is_closing:
                raise RuntimeError("HTTP会话管理器正在关闭，无法创建新会话")
            
            # 检查现有会话是否可用
            if exchange_name in self._sessions:
                session = self._sessions[exchange_name]
                if not session.closed:
                    self.logger.debug(f"复用现有HTTP会话: {exchange_name}")
                    return session
                else:
                    # 清理已关闭的会话
                    self.logger.warning(f"检测到已关闭的会话，清理: {exchange_name}")
                    await self._cleanup_session(exchange_name)
            
            # 🔥 创建SSL上下文 - 修复SSL证书验证问题
            ssl_context = self._create_ssl_context()

            # 🔥 网络优化：使用统一网络配置管理器
            network_config = get_network_config_manager()
            http_config = network_config.get_http_config()  # 保持连接60秒

            # 创建新会话 - 🔥 网络优化配置（修复Bybit API超时问题）
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(
                    total=http_config['total_timeout'],  # 🔥 修复：10秒总超时（解决Bybit API响应慢）
                    connect=http_config['connection_timeout']  # 🔥 修复：5秒连接超时（解决Bybit连接超时）
                ),
                connector=aiohttp.TCPConnector(
                    limit=100,  # 最大连接数保持100
                    limit_per_host=http_config['limit_per_host'],  # 🔥 优化：每个主机20连接
                    ttl_dns_cache=300,  # DNS缓存5分钟
                    use_dns_cache=True,
                    keepalive_timeout=http_config['keepalive_timeout'],  # 🔥 优化：保持连接60秒
                    enable_cleanup_closed=True,  # 启用清理已关闭连接
                    ssl=ssl_context  # 🔥 应用SSL配置
                )
            )
            
            # 存储会话和创建时间
            self._sessions[exchange_name] = session
            self._session_created_time[exchange_name] = time.time()
            
            # 添加弱引用跟踪
            weak_ref = weakref.ref(session, self._session_cleanup_callback)
            self._active_sessions.add(weak_ref)
            
            self._total_sessions_created += 1
            
            self.logger.info(f"✅ 创建新HTTP会话: {exchange_name} (总计: {self._total_sessions_created})")
            
            return session

    async def fetch_with_retry(
        self,
        exchange_name: str,
        url: str,
        method: str = "GET",
        max_retries: Optional[int] = None,
        retry_delay: Optional[float] = None,
        market_type: str = "spot",
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        🔥 统一重试机制 - 实现96%抖动减少效果 + 多端点智能切换
        按照网络优化.md建议实现的重试逻辑

        Args:
            exchange_name: 交易所名称
            url: 请求URL
            method: HTTP方法
            max_retries: 最大重试次数（从环境变量读取，默认3）
            retry_delay: 重试延迟毫秒（从环境变量读取，默认50ms）
            market_type: 市场类型（spot/futures）
            **kwargs: 其他aiohttp参数

        Returns:
            Optional[Dict[str, Any]]: 响应数据或None
        """
        # 🔥 使用统一网络配置管理器
        network_config = get_network_config_manager()
        retry_config = network_config.get_retry_config()

        # 确保类型正确
        max_retries_val: int = max_retries if max_retries is not None else retry_config['max_retries']
        retry_delay_val: float = retry_delay if retry_delay is not None else retry_config['retry_delay']

        # 🔥 新增：多端点智能切换逻辑
        current_endpoint = None
        failed_endpoints = set()

        for _ in range(3):  # 最多尝试3个端点
            # 选择最佳端点
            if url.startswith('http'):
                # 如果传入的是完整URL，提取基础端点
                from urllib.parse import urlparse
                parsed = urlparse(url)
                base_endpoint = f"{parsed.scheme}://{parsed.netloc}"
                current_endpoint = self._select_best_api_endpoint(exchange_name, market_type,
                                                                failed_endpoint=base_endpoint if base_endpoint in failed_endpoints else None)
                if current_endpoint and current_endpoint != base_endpoint:
                    # 替换URL中的端点
                    url = url.replace(base_endpoint, current_endpoint)
            else:
                # 相对路径，需要获取完整端点
                current_endpoint = self._select_best_api_endpoint(exchange_name, market_type)
                if current_endpoint:
                    url = f"{current_endpoint}{url if url.startswith('/') else '/' + url}"

            if not current_endpoint:
                self.logger.error(f"❌ 无可用REST API端点: {exchange_name}")
                break

            session = await self.get_session(exchange_name)

            # 🔥 修复：初始化最佳结果变量
            best_delay = float('inf')
            best_result = None

            # 🔥 并行请求策略：同时发送多个请求，选择最快的
            tasks = []
            for retry in range(max_retries_val):
                task = asyncio.create_task(self._single_request(session, method, url, exchange_name, retry, **kwargs))
                tasks.append(task)

                # 🚀 手动极限优化：真正的零延迟并行请求
                # 移除所有延迟，让所有请求真正并行启动

            # 等待所有请求完成或第一个成功
            completed_tasks = []
            try:
                for task in asyncio.as_completed(tasks):
                    result, delay, success = await task
                    completed_tasks.append(task)

                    if success:
                        # 🔥 修复：立即设置最佳结果，确保第一个成功就被记录
                        if delay < best_delay:
                            best_delay = delay
                            best_result = result

                            # 记录端点成功
                            self._record_endpoint_success(exchange_name, current_endpoint)

                            # 找到第一个成功的就可以取消其他任务
                            for remaining_task in tasks:
                                if remaining_task not in completed_tasks and not remaining_task.done():
                                    remaining_task.cancel()
                            break

            except Exception as e:
                self.logger.debug(f"⚠️ {exchange_name} 并行请求异常: {str(e)[:50]}")

            finally:
                # 确保所有任务都被取消
                for task in tasks:
                    if not task.done():
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass

            # 如果当前端点成功，返回结果
            if best_result is not None:
                self.logger.debug(f"✅ {exchange_name} 端点请求成功: {current_endpoint}, 延迟: {best_delay:.1f}ms")
                return best_result

            # 当前端点失败，记录失败并尝试下一个端点
            self.logger.warning(f"⚠️ {exchange_name} 端点失败: {current_endpoint}")
            self._record_endpoint_failure(exchange_name, current_endpoint)
            failed_endpoints.add(current_endpoint)

            # 等待一小段时间再尝试下一个端点
            await asyncio.sleep(0.1)

        # 所有端点都失败
        self.logger.error(f"❌ {exchange_name} 所有REST API端点都失败")
        return None

    async def _single_request(
        self,
        session: aiohttp.ClientSession,
        method: str,
        url: str,
        exchange_name: str,
        retry_num: int,
        **kwargs
    ) -> tuple[Optional[Dict[str, Any]], float, bool]:
        """
        🔥 单次请求实现 - 网络抖动优化核心
        
        Args:
            session: HTTP会话
            method: HTTP方法
            url: 请求URL
            exchange_name: 交易所名称
            retry_num: 重试次数
            **kwargs: 其他参数
            
        Returns:
            tuple: (结果数据, 延迟ms, 是否成功)
        """
        start_time = time.time()
        
        try:
            # 🔥 核心优化：短超时 + 快速失败
            request_timeout = aiohttp.ClientTimeout(total=2.0, connect=0.5)
            
            self.logger.debug(f"🔄 {exchange_name} 第{retry_num+1}次请求: {url}")
            
            if method.upper() == "GET":
                async with session.get(url, timeout=request_timeout, **kwargs) as response:
                    if response.status == 200:
                        result = await response.json()
                        delay = (time.time() - start_time) * 1000
                        
                        self.logger.debug(f"✅ {exchange_name} 请求成功: {delay:.1f}ms")
                        return result, delay, True
                    else:
                        delay = (time.time() - start_time) * 1000
                        self.logger.debug(f"❌ {exchange_name} HTTP错误: {response.status}, {delay:.1f}ms")
                        return None, delay, False
            else:
                # 支持其他HTTP方法
                async with session.request(method, url, timeout=request_timeout, **kwargs) as response:
                    if response.status == 200:
                        result = await response.json()
                        delay = (time.time() - start_time) * 1000
                        return result, delay, True
                    else:
                        delay = (time.time() - start_time) * 1000
                        return None, delay, False
                        
        except asyncio.TimeoutError:
            delay = (time.time() - start_time) * 1000
            self.logger.debug(f"⏱️ {exchange_name} 请求超时: {delay:.1f}ms")
            return None, delay, False
            
        except Exception as e:
            delay = (time.time() - start_time) * 1000
            self.logger.debug(f"❌ {exchange_name} 请求异常: {str(e)[:30]}, {delay:.1f}ms")
            return None, delay, False

    def _session_cleanup_callback(self, weak_ref):
        """会话清理回调函数"""
        self._active_sessions.discard(weak_ref)
        self.logger.debug("会话弱引用已清理")
    
    async def _cleanup_session(self, exchange_name: str):
        """清理指定交易所的会话"""
        if exchange_name in self._sessions:
            session = self._sessions[exchange_name]
            if not session.closed:
                await session.close()
                self._total_sessions_closed += 1
                self.logger.debug(f"关闭HTTP会话: {exchange_name}")
            
            del self._sessions[exchange_name]
            self._session_created_time.pop(exchange_name, None)
    
    async def close_session(self, exchange_name: str):
        """
        关闭指定交易所的HTTP会话
        
        Args:
            exchange_name: 交易所名称
        """
        async with self._lock:
            await self._cleanup_session(exchange_name)
            self.logger.info(f"✅ 手动关闭HTTP会话: {exchange_name}")
    
    async def close_all_sessions(self):
        """关闭所有HTTP会话"""
        async with self._lock:
            self._is_closing = True

            self.logger.info(f"🔄 开始关闭所有HTTP会话 (共{len(self._sessions)}个)")

            # 关闭所有会话
            close_tasks = []
            for exchange_name in list(self._sessions.keys()):
                task = asyncio.create_task(self._cleanup_session(exchange_name))
                close_tasks.append(task)

            if close_tasks:
                await asyncio.gather(*close_tasks, return_exceptions=True)

            # 清理状态
            self._sessions.clear()
            self._session_created_time.clear()

            # 🔥 修复：重置关闭状态，允许重新创建会话
            self._is_closing = False

            self.logger.info(f"✅ 所有HTTP会话已关闭 (创建: {self._total_sessions_created}, 关闭: {self._total_sessions_closed})")
    
    @asynccontextmanager
    async def session_context(self, exchange_name: str):
        """
        HTTP会话异步上下文管理器
        
        Args:
            exchange_name: 交易所名称
            
        Usage:
            async with session_manager.session_context("gate") as session:
                async with session.get(url) as response:
                    data = await response.json()
        """
        session = await self.get_session(exchange_name)
        try:
            yield session
        except Exception as e:
            self.logger.error(f"HTTP会话使用异常: {exchange_name}, {e}")
            raise
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        active_sessions = {}
        for exchange_name, session in self._sessions.items():
            created_time = self._session_created_time.get(exchange_name, 0)
            active_sessions[exchange_name] = {
                "created_time": created_time,
                "age_seconds": time.time() - created_time,
                "closed": session.closed,
                "connector_info": {
                    "limit": session.connector.limit if session.connector and hasattr(session.connector, 'limit') else None,
                    "limit_per_host": session.connector.limit_per_host if session.connector and hasattr(session.connector, 'limit_per_host') else None
                }
            }
        
        return {
            "active_sessions": active_sessions,
            "total_created": self._total_sessions_created,
            "total_closed": self._total_sessions_closed,
            "active_count": len(self._sessions),
            "weak_refs_count": len(self._active_sessions),
            "is_closing": self._is_closing
        }
    
    async def health_check(self) -> bool:
        """健康检查：验证所有会话状态"""
        try:
            async with self._lock:
                unhealthy_sessions = []
                
                for exchange_name, session in self._sessions.items():
                    if session.closed:
                        unhealthy_sessions.append(exchange_name)
                
                # 清理不健康的会话
                for exchange_name in unhealthy_sessions:
                    self.logger.warning(f"发现不健康会话，清理: {exchange_name}")
                    await self._cleanup_session(exchange_name)
                
                healthy_count = len(self._sessions) - len(unhealthy_sessions)
                self.logger.debug(f"健康检查完成: {healthy_count}个健康会话, {len(unhealthy_sessions)}个已清理")
                
                return len(unhealthy_sessions) == 0
                
        except Exception as e:
            self.logger.error(f"健康检查异常: {e}")
            return False


# 🔥 全局单例实例
_session_manager_instance: Optional[UnifiedHttpSessionManager] = None

def get_unified_session_manager() -> UnifiedHttpSessionManager:
    """获取统一HTTP会话管理器单例实例"""
    global _session_manager_instance
    if _session_manager_instance is None:
        _session_manager_instance = UnifiedHttpSessionManager()
    return _session_manager_instance

async def cleanup_all_http_sessions():
    """清理所有HTTP会话 - 系统关闭时调用"""
    global _session_manager_instance
    if _session_manager_instance:
        await _session_manager_instance.close_all_sessions()
        # 🔥 修复：系统关闭时才重置实例，否则保留实例以便重用
        # _session_manager_instance = None
