#!/usr/bin/env python3
"""
🔥 多节点智能切换和心跳监控测试脚本
用于验证HTTP 503错误解决方案和心跳监控功能

测试内容：
1. REST API多端点智能切换功能
2. WebSocket多节点智能切换功能  
3. 心跳监控详细日志记录
4. HTTP 503错误自动恢复能力
5. 端点健康度评估和切换逻辑

使用方法：
python scripts/test_multi_node_heartbeat.py --exchange okx --test-type all
"""

import asyncio
import sys
import os
import time
import argparse
import json
from typing import Dict, List, Optional, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.unified_http_session_manager import UnifiedHttpSessionManager
from websocket.unified_connection_pool_manager import UnifiedConnectionPoolManager
from websocket.heartbeat_monitor import get_heartbeat_monitor, HeartbeatStatus
from utils.logger import get_logger


class MultiNodeHeartbeatTester:
    """🔥 多节点智能切换和心跳监控测试器"""
    
    def __init__(self, exchange: str = "okx"):
        self.exchange = exchange
        self.logger = get_logger(f"MultiNodeTester-{exchange}")
        
        # 初始化管理器
        self.http_manager = UnifiedHttpSessionManager()
        self.ws_manager = UnifiedConnectionPoolManager()
        self.heartbeat_monitor = get_heartbeat_monitor()
        
        # 测试结果
        self.test_results = {
            "rest_api_multi_endpoint": {"status": "未测试", "details": []},
            "websocket_multi_node": {"status": "未测试", "details": []},
            "heartbeat_monitoring": {"status": "未测试", "details": []},
            "http_503_recovery": {"status": "未测试", "details": []},
            "endpoint_health_assessment": {"status": "未测试", "details": []}
        }

    async def run_all_tests(self):
        """🔥 运行所有测试"""
        self.logger.info("🚀 开始多节点智能切换和心跳监控测试")
        
        try:
            # 启动心跳监控
            await self.heartbeat_monitor.start_monitoring()
            
            # 1. 测试REST API多端点切换
            await self.test_rest_api_multi_endpoint()
            
            # 2. 测试WebSocket多节点切换
            await self.test_websocket_multi_node()
            
            # 3. 测试心跳监控功能
            await self.test_heartbeat_monitoring()
            
            # 4. 测试HTTP 503错误恢复
            await self.test_http_503_recovery()
            
            # 5. 测试端点健康度评估
            await self.test_endpoint_health_assessment()
            
            # 生成测试报告
            self.generate_test_report()
            
        except Exception as e:
            self.logger.error(f"测试执行失败: {e}")
        finally:
            await self.heartbeat_monitor.stop_monitoring()

    async def test_rest_api_multi_endpoint(self):
        """🔥 测试REST API多端点智能切换"""
        self.logger.info("📡 测试REST API多端点智能切换...")
        
        try:
            # 测试正常请求
            test_url = "/api/v5/public/time" if self.exchange == "okx" else "/api/v4/spot/time"
            
            # 发送多个请求测试端点切换
            success_count = 0
            total_requests = 5
            
            for i in range(total_requests):
                result = await self.http_manager.fetch_with_retry(
                    exchange_name=self.exchange,
                    url=test_url,
                    method="GET",
                    market_type="spot"
                )
                
                if result:
                    success_count += 1
                    self.logger.info(f"✅ REST API请求 {i+1} 成功")
                else:
                    self.logger.warning(f"❌ REST API请求 {i+1} 失败")
                
                await asyncio.sleep(0.5)
            
            success_rate = (success_count / total_requests) * 100
            
            if success_rate >= 80:
                self.test_results["rest_api_multi_endpoint"]["status"] = "通过"
                self.test_results["rest_api_multi_endpoint"]["details"].append(f"成功率: {success_rate}%")
            else:
                self.test_results["rest_api_multi_endpoint"]["status"] = "失败"
                self.test_results["rest_api_multi_endpoint"]["details"].append(f"成功率过低: {success_rate}%")
                
        except Exception as e:
            self.test_results["rest_api_multi_endpoint"]["status"] = "异常"
            self.test_results["rest_api_multi_endpoint"]["details"].append(f"测试异常: {str(e)}")
            self.logger.error(f"REST API多端点测试异常: {e}")

    async def test_websocket_multi_node(self):
        """🔥 测试WebSocket多节点智能切换"""
        self.logger.info("🔌 测试WebSocket多节点智能切换...")
        
        try:
            # 获取连接池配置
            endpoints = self.ws_manager._get_available_endpoints(self.exchange, "futures")
            
            if not endpoints:
                self.test_results["websocket_multi_node"]["status"] = "失败"
                self.test_results["websocket_multi_node"]["details"].append("未找到可用端点")
                return
            
            # 测试端点选择逻辑
            best_endpoint = await self.ws_manager._select_best_endpoint(self.exchange, "futures")
            
            if best_endpoint:
                self.test_results["websocket_multi_node"]["status"] = "通过"
                self.test_results["websocket_multi_node"]["details"].append(f"选择最佳端点: {best_endpoint.url}")
                self.test_results["websocket_multi_node"]["details"].append(f"可用端点数量: {len(endpoints)}")
            else:
                self.test_results["websocket_multi_node"]["status"] = "失败"
                self.test_results["websocket_multi_node"]["details"].append("无法选择最佳端点")
                
        except Exception as e:
            self.test_results["websocket_multi_node"]["status"] = "异常"
            self.test_results["websocket_multi_node"]["details"].append(f"测试异常: {str(e)}")
            self.logger.error(f"WebSocket多节点测试异常: {e}")

    async def test_heartbeat_monitoring(self):
        """🔥 测试心跳监控功能"""
        self.logger.info("💓 测试心跳监控功能...")
        
        try:
            # 模拟心跳事件
            from websocket.heartbeat_monitor import HeartbeatFailureReason
            
            # 记录成功心跳
            for i in range(3):
                self.heartbeat_monitor.record_heartbeat_event(
                    exchange=self.exchange,
                    success=True,
                    response_time_ms=50.0 + i * 10
                )
                await asyncio.sleep(0.1)
            
            # 记录失败心跳
            for i in range(2):
                self.heartbeat_monitor.record_heartbeat_event(
                    exchange=self.exchange,
                    success=False,
                    response_time_ms=1000.0,
                    failure_reason=HeartbeatFailureReason.NETWORK_TIMEOUT,
                    error_message="模拟网络超时"
                )
                await asyncio.sleep(0.1)
            
            # 检查监控状态
            status = self.heartbeat_monitor.get_heartbeat_status(self.exchange)
            
            if status and "metrics" in status:
                metrics = status["metrics"]
                self.test_results["heartbeat_monitoring"]["status"] = "通过"
                self.test_results["heartbeat_monitoring"]["details"].append(f"总发送: {metrics['total_sent']}")
                self.test_results["heartbeat_monitoring"]["details"].append(f"成功率: {metrics['success_rate']:.1f}%")
                self.test_results["heartbeat_monitoring"]["details"].append(f"平均响应时间: {metrics['avg_response_time']:.1f}ms")
            else:
                self.test_results["heartbeat_monitoring"]["status"] = "失败"
                self.test_results["heartbeat_monitoring"]["details"].append("无法获取心跳状态")
                
        except Exception as e:
            self.test_results["heartbeat_monitoring"]["status"] = "异常"
            self.test_results["heartbeat_monitoring"]["details"].append(f"测试异常: {str(e)}")
            self.logger.error(f"心跳监控测试异常: {e}")

    async def test_http_503_recovery(self):
        """🔥 测试HTTP 503错误恢复能力"""
        self.logger.info("🚨 测试HTTP 503错误恢复能力...")
        
        try:
            # 模拟HTTP 503错误场景
            # 这里我们测试端点切换逻辑是否能处理失败的端点
            
            # 记录端点失败
            failed_endpoint = "https://www.okx.com"  # 模拟失败的端点
            self.http_manager._record_endpoint_failure(self.exchange, failed_endpoint)
            
            # 尝试选择新的端点
            new_endpoint = self.http_manager._select_best_api_endpoint(
                self.exchange, 
                "spot", 
                failed_endpoint=failed_endpoint
            )
            
            if new_endpoint and new_endpoint != failed_endpoint:
                self.test_results["http_503_recovery"]["status"] = "通过"
                self.test_results["http_503_recovery"]["details"].append(f"成功切换到备用端点: {new_endpoint}")
                self.test_results["http_503_recovery"]["details"].append("HTTP 503错误可通过端点切换解决")
            else:
                self.test_results["http_503_recovery"]["status"] = "失败"
                self.test_results["http_503_recovery"]["details"].append("无法切换到备用端点")
                
        except Exception as e:
            self.test_results["http_503_recovery"]["status"] = "异常"
            self.test_results["http_503_recovery"]["details"].append(f"测试异常: {str(e)}")
            self.logger.error(f"HTTP 503恢复测试异常: {e}")

    async def test_endpoint_health_assessment(self):
        """🔥 测试端点健康度评估"""
        self.logger.info("🏥 测试端点健康度评估...")
        
        try:
            # 检查HTTP管理器的端点配置
            endpoints = self.http_manager._api_endpoints.get(self.exchange, [])
            
            if endpoints:
                self.test_results["endpoint_health_assessment"]["status"] = "通过"
                self.test_results["endpoint_health_assessment"]["details"].append(f"配置端点数量: {len(endpoints)}")
                
                for i, endpoint_config in enumerate(endpoints):
                    spot_url = endpoint_config.get("spot", "未配置")
                    futures_url = endpoint_config.get("futures", "未配置")
                    priority = endpoint_config.get("priority", 0)
                    is_backup = endpoint_config.get("is_backup", False)
                    
                    self.test_results["endpoint_health_assessment"]["details"].append(
                        f"端点{i+1}: spot={spot_url}, futures={futures_url}, "
                        f"优先级={priority}, 备用={'是' if is_backup else '否'}"
                    )
            else:
                self.test_results["endpoint_health_assessment"]["status"] = "失败"
                self.test_results["endpoint_health_assessment"]["details"].append("未配置任何端点")
                
        except Exception as e:
            self.test_results["endpoint_health_assessment"]["status"] = "异常"
            self.test_results["endpoint_health_assessment"]["details"].append(f"测试异常: {str(e)}")
            self.logger.error(f"端点健康度评估测试异常: {e}")

    def generate_test_report(self):
        """🔥 生成测试报告"""
        self.logger.info("📊 生成测试报告...")
        
        print("\n" + "="*80)
        print(f"🔥 多节点智能切换和心跳监控测试报告 - {self.exchange.upper()}")
        print("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["status"] == "通过")
        
        print(f"📈 测试概览: {passed_tests}/{total_tests} 通过")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        for test_name, result in self.test_results.items():
            status_icon = {
                "通过": "✅",
                "失败": "❌", 
                "异常": "💥",
                "未测试": "⏸️"
            }.get(result["status"], "❓")
            
            print(f"{status_icon} {test_name}: {result['status']}")
            for detail in result["details"]:
                print(f"   └─ {detail}")
            print()
        
        # 针对HTTP 503错误的具体建议
        if self.test_results["http_503_recovery"]["status"] == "通过":
            print("🎯 HTTP 503错误解决方案:")
            print("   ✅ 多端点智能切换功能已就绪")
            print("   ✅ 可以自动处理服务器拒绝连接的情况")
            print("   ✅ 建议在生产环境中启用此功能")
        else:
            print("⚠️ HTTP 503错误解决方案需要完善:")
            print("   🔧 建议检查端点配置")
            print("   🔧 建议测试网络连接")
        
        print("\n" + "="*80)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="多节点智能切换和心跳监控测试")
    parser.add_argument("--exchange", default="okx", choices=["okx", "gate", "bybit"], 
                       help="测试的交易所")
    parser.add_argument("--test-type", default="all", 
                       choices=["all", "rest", "websocket", "heartbeat", "recovery", "health"],
                       help="测试类型")
    
    args = parser.parse_args()
    
    tester = MultiNodeHeartbeatTester(args.exchange)
    
    if args.test_type == "all":
        await tester.run_all_tests()
    elif args.test_type == "rest":
        await tester.test_rest_api_multi_endpoint()
        tester.generate_test_report()
    elif args.test_type == "websocket":
        await tester.test_websocket_multi_node()
        tester.generate_test_report()
    elif args.test_type == "heartbeat":
        await tester.heartbeat_monitor.start_monitoring()
        await tester.test_heartbeat_monitoring()
        await tester.heartbeat_monitor.stop_monitoring()
        tester.generate_test_report()
    elif args.test_type == "recovery":
        await tester.test_http_503_recovery()
        tester.generate_test_report()
    elif args.test_type == "health":
        await tester.test_endpoint_health_assessment()
        tester.generate_test_report()


if __name__ == "__main__":
    asyncio.run(main())
