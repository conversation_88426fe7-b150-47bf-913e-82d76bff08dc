#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX WebSocket数据流阻塞和连接问题深度诊断脚本

🔥 关键问题发现：
1. OKX WebSocket HTTP 503错误
2. WebSocket重连参数错误：takes 1 positional argument but 2 were given  
3. OKX API限速错误（25次Too Many Requests）
4. error_handler中TypeError: 'int' object is not iterable
5. 数据流阻塞30秒以上

基于logs和代码分析的精确诊断
"""

import sys
import os
import json
import time
import asyncio
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append('/root/myproject/123/67E okx 解决了 gate还有问题/123')

class OKXWebSocketCriticalDiagnosis:
    """OKX WebSocket关键问题诊断器"""
    
    def __init__(self):
        self.results = {
            "diagnosis_time": datetime.now().isoformat(),
            "critical_issues": {},
            "code_analysis": {},
            "recommendations": []
        }
    
    def analyze_logs(self):
        """🔥 分析关键错误日志"""
        print("🔍 分析logs中的关键错误...")
        
        log_files = [
            "/root/myproject/123/67E okx 解决了 gate还有问题/123/logs/error_20250802.log",
            "/root/myproject/123/67E okx 解决了 gate还有问题/123/logs/websocket_error_recovery_20250802.log",
            "/root/myproject/123/67E okx 解决了 gate还有问题/123/logs/OKXExchange.log"
        ]
        
        issues = {
            "http_503_error": 0,
            "reconnect_parameter_errors": 0,
            "api_rate_limit_errors": 0,
            "type_error_not_iterable": 0,
            "data_flow_blocking_duration": 0
        }
        
        for log_file in log_files:
            if Path(log_file).exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 🔥 关键问题1：HTTP 503错误
                if "server rejected WebSocket connection: HTTP 503" in content:
                    issues["http_503_error"] += content.count("HTTP 503")
                
                # 🔥 关键问题2：重连参数错误
                if "_reconnect() takes 1 positional argument but 2 were given" in content:
                    issues["reconnect_parameter_errors"] += content.count("takes 1 positional argument but 2 were given")
                
                # 🔥 关键问题3：API限速
                if "Too Many Requests" in content:
                    issues["api_rate_limit_errors"] += content.count("Too Many Requests")
                
                # 🔥 关键问题4：TypeError
                if "'int' object is not iterable" in content:
                    issues["type_error_not_iterable"] += content.count("'int' object is not iterable")
        
        self.results["critical_issues"] = issues
        
        # 判断严重程度
        severity = "CRITICAL" if issues["http_503_error"] > 0 or issues["reconnect_parameter_errors"] > 0 else "HIGH"
        
        print(f"📊 关键问题统计：")
        print(f"   🔥 HTTP 503错误: {issues['http_503_error']}次")
        print(f"   🔥 重连参数错误: {issues['reconnect_parameter_errors']}次")
        print(f"   🔥 API限速错误: {issues['api_rate_limit_errors']}次")
        print(f"   🔥 TypeError错误: {issues['type_error_not_iterable']}次")
        print(f"   🚨 问题严重程度: {severity}")
        
        return issues
    
    def analyze_websocket_code_issues(self):
        """🔥 分析WebSocket代码实现问题"""
        print("🔍 分析WebSocket代码实现问题...")
        
        code_issues = {
            "okx_vs_bybit_gate_differences": [],
            "error_handler_bugs": [],
            "api_rate_limit_config": {}
        }
        
        # 🔥 问题1：三交易所WebSocket实现差异
        okx_file = "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/okx_ws.py"
        bybit_file = "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/bybit_ws.py"
        gate_file = "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/gate_ws.py"
        
        # 对比时间戳处理器初始化
        files_to_check = [
            (okx_file, "OKX"),
            (bybit_file, "Bybit"), 
            (gate_file, "Gate.io")
        ]
        
        for file_path, exchange in files_to_check:
            if Path(file_path).exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                has_timestamp_processor = "self.timestamp_processor = get_timestamp_processor" in content
                has_data_flow_monitor = "_monitor_data_flow" in content or "last_data_time" in content
                
                code_issues["okx_vs_bybit_gate_differences"].append({
                    "exchange": exchange,
                    "has_timestamp_processor": has_timestamp_processor,
                    "has_data_flow_monitor": has_data_flow_monitor
                })
        
        # 🔥 问题2：error_handler中的错误
        error_handler_file = "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/error_handler.py"
        if Path(error_handler_file).exists():
            with open(error_handler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查line 288: sum(len([e for e in self.error_events if e.retry_count > 0]))
            if "sum(len([e for e in self.error_events" in content:
                code_issues["error_handler_bugs"].append({
                    "issue": "error_events可能不是list类型，导致TypeError",
                    "location": "error_handler.py:288-289", 
                    "bug_code": "sum(len([e for e in self.error_events..."
                })
        
        # 🔥 问题3：API限速配置检查
        okx_exchange_file = "/root/myproject/123/67E okx 解决了 gate还有问题/123/exchanges/okx_exchange.py"
        if Path(okx_exchange_file).exists():
            with open(okx_exchange_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查API限速配置
            if "rate_limit" in content:
                # 简单提取rate_limit配置
                lines = content.split('\n')
                for line in lines:
                    if "rate_limit" in line and "=" in line:
                        code_issues["api_rate_limit_config"]["okx_rate_limit"] = line.strip()
                        break
        
        self.results["code_analysis"] = code_issues
        
        print(f"📊 代码问题分析：")
        for diff in code_issues["okx_vs_bybit_gate_differences"]:
            print(f"   {diff['exchange']}: 时间戳处理器={diff['has_timestamp_processor']}, 数据流监控={diff['has_data_flow_monitor']}")
        
        if code_issues["error_handler_bugs"]:
            print(f"   🚨 error_handler bug: {code_issues['error_handler_bugs'][0]['issue']}")
        
        return code_issues
    
    def check_official_sdk_compliance(self):
        """🔥 检查官方SDK规范遵循情况"""
        print("🔍 检查官方SDK规范遵循...")
        
        # 检查是否存在官方SDK
        okx_sdk_path = "/root/myproject/123/67E okx 解决了 gate还有问题/官方SDK/okx-sdk-master"
        
        sdk_compliance = {
            "okx_official_sdk_available": Path(okx_sdk_path).exists(),
            "websocket_v5_compliance": False,
            "heartbeat_mechanism": "未检查"
        }
        
        if sdk_compliance["okx_official_sdk_available"]:
            print("   ✅ OKX官方SDK可用")
            
            # 简单检查WebSocket v5规范
            okx_ws_file = "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/okx_ws.py"
            if Path(okx_ws_file).exists():
                with open(okx_ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查WebSocket URL是否符合v5规范
                if "wss://ws.okx.com:8443/ws/v5/public" in content:
                    sdk_compliance["websocket_v5_compliance"] = True
                    
                # 检查心跳机制
                if '"ping"' in content or 'ping' in content:
                    sdk_compliance["heartbeat_mechanism"] = "支持ping-pong"
                else:
                    sdk_compliance["heartbeat_mechanism"] = "无心跳机制"
        
        print(f"   WebSocket v5规范: {'✅' if sdk_compliance['websocket_v5_compliance'] else '❌'}")
        print(f"   心跳机制: {sdk_compliance['heartbeat_mechanism']}")
        
        return sdk_compliance
    
    def generate_fix_recommendations(self):
        """🔥 生成修复建议"""
        print("🔧 生成修复建议...")
        
        recommendations = [
            {
                "priority": "CRITICAL",
                "issue": "OKX WebSocket HTTP 503错误", 
                "fix": "1) 检查网络连接 2) 增加重连延迟 3) 使用备用WebSocket端点"
            },
            {
                "priority": "CRITICAL", 
                "issue": "WebSocket重连参数错误",
                "fix": "修复_reconnect()方法调用，确保参数匹配：检查error_handler中的调用代码"
            },
            {
                "priority": "HIGH",
                "issue": "OKX API限速（25次Too Many Requests）", 
                "fix": "1) 降低API调用频率到1次/秒 2) 增加指数退避重试 3) 批处理API请求"
            },
            {
                "priority": "HIGH",
                "issue": "error_handler TypeError: 'int' object is not iterable",
                "fix": "修复error_handler.py:288-289行，确保error_events是list类型"
            },
            {
                "priority": "MEDIUM",
                "issue": "数据流阻塞30秒以上",
                "fix": "1) 增加数据流监控 2) 实现自动重置连接 3) 优化心跳机制"
            },
            {
                "priority": "MEDIUM", 
                "issue": "统一时间戳处理器集成",
                "fix": "确保OKX WebSocket正确使用统一时间戳处理器，与Bybit/Gate.io保持一致"
            }
        ]
        
        self.results["recommendations"] = recommendations
        
        print("🎯 修复建议（按优先级排序）：")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. [{rec['priority']}] {rec['issue']}")
            print(f"      修复方案: {rec['fix']}")
        
        return recommendations
    
    def run_diagnosis(self):
        """🔥 执行完整诊断"""
        print("🚀 开始OKX WebSocket关键问题诊断...")
        print("=" * 80)
        
        # 执行各项诊断
        log_issues = self.analyze_logs()
        code_issues = self.analyze_websocket_code_issues()
        sdk_compliance = self.check_official_sdk_compliance()
        recommendations = self.generate_fix_recommendations()
        
        # 生成综合评估
        critical_count = sum(1 for r in recommendations if r['priority'] == 'CRITICAL')
        high_count = sum(1 for r in recommendations if r['priority'] == 'HIGH')
        
        overall_severity = "CRITICAL" if critical_count > 0 else ("HIGH" if high_count > 0 else "MEDIUM")
        
        print("\n" + "=" * 80)
        print("📋 诊断总结")
        print("=" * 80)
        print(f"🔥 总体严重程度: {overall_severity}")
        print(f"🚨 关键问题数量: {critical_count}")
        print(f"⚠️  高优先级问题: {high_count}")
        print(f"📊 API限速错误: {log_issues.get('api_rate_limit_errors', 0)}次")
        print(f"🔄 重连失败次数: {log_issues.get('reconnect_parameter_errors', 0)}次")
        
        # 保存诊断结果
        output_file = f"/root/myproject/123/67E okx 解决了 gate还有问题/123/diagnostic_results/okx_websocket_critical_diagnosis_{int(time.time())}.json"
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 诊断结果已保存到: {output_file}")
        
        # 返回关键信息供后续修复使用
        return {
            "severity": overall_severity,
            "critical_issues": log_issues,
            "fix_priority": [r for r in recommendations if r['priority'] == 'CRITICAL']
        }

def main():
    """主函数"""
    try:
        diagnosis = OKXWebSocketCriticalDiagnosis()
        result = diagnosis.run_diagnosis()
        
        # 输出最终修复指导
        print("\n🎯 下一步修复指导：")
        if result["severity"] == "CRITICAL":
            print("⚡ 立即修复CRITICAL级别问题")
            for issue in result["fix_priority"]:
                print(f"   - {issue['issue']}: {issue['fix']}")
        else:
            print("✅ 无CRITICAL问题，可按优先级逐步修复")
        
        return result
        
    except Exception as e:
        print(f"❌ 诊断过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()