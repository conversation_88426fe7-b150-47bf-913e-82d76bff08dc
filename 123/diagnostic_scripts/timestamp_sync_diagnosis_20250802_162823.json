{"diagnosis_timestamp": "2025-08-02T16:28:23.814398", "diagnosis_summary": {"overall_status": "NEEDS_ATTENTION", "critical_issues": ["交易所时间未同步: gate, bybit, okx"], "potential_causes": ["交易所时间API访问失败或网络问题"], "recommendations": ["检查网络连接和交易所时间API的可访问性"]}, "detailed_results": {"processor_status": {"processors_available": true, "processors_status": {"gate": {"exchange": "gate", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}, "bybit": {"exchange": "bybit", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}, "okx": {"exchange": "okx", "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}, "sync_methods_working": {"gate": {"success": true, "duration": 0.41547179222106934}, "bybit": {"success": true, "duration": 0.12987303733825684}, "okx": {"success": true, "duration": 0.13547968864440918}}, "timestamp_generation_test": {"gate": {"generated_timestamp": 1754144902038, "current_time": 1754144902038, "time_diff": 0}, "bybit": {"generated_timestamp": 1754144902131, "current_time": 1754144902168, "time_diff": 37}, "okx": {"generated_timestamp": 1754144902274, "current_time": 1754144902303, "time_diff": 29}}, "error_details": []}, "sync_validation": {"simulation_successful": true, "test_scenarios": [{"scenario": "正常同步", "time_diff": 50, "expected_sync": true, "actual_results": {"gate": {"is_synced": true, "actual_time_diff": 50, "matches_expected": true}, "bybit": {"is_synced": true, "actual_time_diff": 50, "matches_expected": true}, "okx": {"is_synced": true, "actual_time_diff": 50, "matches_expected": true}}, "all_consistent": true}, {"scenario": "边界情况", "time_diff": 1000, "expected_sync": true, "actual_results": {"gate": {"is_synced": true, "actual_time_diff": 1000, "matches_expected": true}, "bybit": {"is_synced": true, "actual_time_diff": 1000, "matches_expected": true}, "okx": {"is_synced": true, "actual_time_diff": 1000, "matches_expected": true}}, "all_consistent": true}, {"scenario": "轻微超标", "time_diff": 1100, "expected_sync": false, "actual_results": {"gate": {"is_synced": false, "actual_time_diff": 1100, "matches_expected": true}, "bybit": {"is_synced": false, "actual_time_diff": 1100, "matches_expected": true}, "okx": {"is_synced": false, "actual_time_diff": 1100, "matches_expected": true}}, "all_consistent": true}, {"scenario": "严重超标", "time_diff": 5000, "expected_sync": false, "actual_results": {"gate": {"is_synced": false, "actual_time_diff": 5000, "matches_expected": true}, "bybit": {"is_synced": false, "actual_time_diff": 5000, "matches_expected": true}, "okx": {"is_synced": false, "actual_time_diff": 5000, "matches_expected": true}}, "all_consistent": true}, {"scenario": "极端超标", "time_diff": 15000, "expected_sync": false, "actual_results": {"gate": {"is_synced": false, "actual_time_diff": 15000, "matches_expected": true}, "bybit": {"is_synced": false, "actual_time_diff": 15000, "matches_expected": true}, "okx": {"is_synced": false, "actual_time_diff": 15000, "matches_expected": true}}, "all_consistent": true}], "validation_logic_working": true, "threshold_analysis": {"configured_threshold": 1000, "threshold_consistent": true, "threshold_reasonable": true}, "error_details": []}, "exchange_comparison": {"comparison_successful": true, "exchange_characteristics": {"gate": {"sample_timestamps": [1754144902306, 1754144902406, 1754144902507, 1754144902607, 1754144902707], "avg_generation_interval": 100.25, "avg_system_time_diff": 301.4, "max_system_time_diff": 502, "timestamp_stability": 1}, "bybit": {"sample_timestamps": [1754144902771, 1754144902872, 1754144902972, 1754144903073, 1754144903173], "avg_generation_interval": 100.5, "avg_system_time_diff": 338.8, "max_system_time_diff": 540, "timestamp_stability": 1}, "okx": {"sample_timestamps": [1754144903282, 1754144903383, 1754144903483, 1754144903584, 1754144903684], "avg_generation_interval": 100.5, "avg_system_time_diff": 329.8, "max_system_time_diff": 531, "timestamp_stability": 1}}, "timestamp_format_analysis": {"all_millisecond_format": true, "format_consistent": true, "notes": "所有交易所应该使用毫秒级时间戳"}, "potential_issues": [], "error_details": []}, "websocket_test": {"extraction_test_successful": true, "mock_data_tests": {"gate": [{"sample_index": 0, "raw_data": {"t": 1754144903813, "result": {"data": "test"}}, "extracted_timestamp": 1754144903813, "time_diff_from_current": 1, "extraction_successful": true}, {"sample_index": 1, "raw_data": {"result": {"t": 1754144903713}}, "extracted_timestamp": 1754144903713, "time_diff_from_current": 101, "extraction_successful": true}, {"sample_index": 2, "raw_data": {}, "extracted_timestamp": 1754144903814, "time_diff_from_current": 0, "extraction_successful": true}], "bybit": [{"sample_index": 0, "raw_data": {"ts": 1754144903814, "data": "test"}, "extracted_timestamp": 1754144903814, "time_diff_from_current": 0, "extraction_successful": true}, {"sample_index": 1, "raw_data": {"ts": 1754144903614}, "extracted_timestamp": 1754144903614, "time_diff_from_current": 200, "extraction_successful": true}, {"sample_index": 2, "raw_data": {}, "extracted_timestamp": 1754144903777, "time_diff_from_current": 37, "extraction_successful": true}], "okx": [{"sample_index": 0, "raw_data": {"ts": "1754144903814", "data": "test"}, "extracted_timestamp": 1754144903814, "time_diff_from_current": 0, "extraction_successful": true}, {"sample_index": 1, "raw_data": {"ts": 1754144903514}, "extracted_timestamp": 1754144903514, "time_diff_from_current": 300, "extraction_successful": true}, {"sample_index": 2, "raw_data": {}, "extracted_timestamp": 1754144903785, "time_diff_from_current": 29, "extraction_successful": true}]}, "extraction_consistency": {"success_rate": 1.0, "total_tests": 9, "successful_tests": 9}, "potential_extraction_issues": [], "error_details": []}}, "root_cause_analysis": {}, "fix_priority": ["HIGH: 修复交易所时间同步问题"]}