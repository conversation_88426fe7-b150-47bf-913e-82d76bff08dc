#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳同步问题精确诊断脚本
根据深度代码分析，精准定位WebSocket时间戳同步失败的根本原因

问题背景：
- 系统声称使用"机构级别100%修复"的统一时间戳处理器
- 但实际运行时出现大量时间差异超过1000ms的时间戳同步失败
- 错误来源：core/opportunity_scanner.py:1881-1899行的validate_cross_exchange_sync调用

诊断目标：
1. 验证统一时间戳处理器的实际工作状态
2. 对比三个交易所WebSocket时间戳的真实情况
3. 分析为什么Bybit相对较少出现错误
4. 精确定位时间戳差异的根本原因
"""

import asyncio
import sys
import os
import time
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def main():
    """主诊断流程"""
    print("🔥 时间戳同步问题精确诊断")
    print("=" * 60)
    
    # 第1步：验证统一时间戳处理器状态
    print("\n📋 第1步：验证统一时间戳处理器实际状态")
    processor_status = await diagnose_timestamp_processors()
    
    # 第2步：模拟OpportunityScanner中的时间戳验证逻辑
    print("\n📋 第2步：模拟OpportunityScanner时间戳验证逻辑")
    sync_validation_results = await simulate_cross_exchange_sync_validation()
    
    # 第3步：分析三个交易所的时间戳差异
    print("\n📋 第3步：分析三个交易所时间戳差异")
    exchange_comparison = await compare_exchange_timestamps()
    
    # 第4步：测试实际WebSocket时间戳提取
    print("\n📋 第4步：测试实际WebSocket时间戳提取")
    websocket_timestamp_test = await test_websocket_timestamp_extraction()
    
    # 第5步：生成诊断报告
    print("\n📋 第5步：生成诊断报告")
    diagnosis_report = generate_diagnosis_report(
        processor_status,
        sync_validation_results, 
        exchange_comparison,
        websocket_timestamp_test
    )
    
    # 保存诊断结果
    save_diagnosis_results(diagnosis_report)
    
    print("\n✅ 诊断完成！请查看诊断报告了解详细结果。")

async def diagnose_timestamp_processors():
    """诊断统一时间戳处理器的实际状态"""
    print("🔍 诊断统一时间戳处理器...")
    
    results = {
        "processors_available": False,
        "processors_status": {},
        "sync_methods_working": {},
        "timestamp_generation_test": {},
        "error_details": []
    }
    
    try:
        from websocket.unified_timestamp_processor import (
            get_timestamp_processor, 
            get_synced_timestamp,
            ensure_milliseconds_timestamp,
            calculate_data_age
        )
        results["processors_available"] = True
        print("✅ 统一时间戳处理器模块导入成功")
        
        # 测试各个交易所的处理器
        exchanges = ["gate", "bybit", "okx"]
        
        for exchange in exchanges:
            print(f"\n🔍 测试 {exchange.upper()} 时间戳处理器:")
            
            try:
                # 获取处理器实例
                processor = get_timestamp_processor(exchange)
                status = processor.get_sync_status()
                
                results["processors_status"][exchange] = status
                
                print(f"  📊 同步状态: {status['time_synced']}")
                print(f"  📊 时间偏移: {status['time_offset_ms']}ms")
                print(f"  📊 最后同步: {status['last_sync_time']}")
                print(f"  📊 重试次数: {status.get('sync_retry_count', 0)}")
                
                # 测试时间同步方法
                print(f"  🔄 测试强制时间同步...")
                sync_start = time.time()
                sync_success = await processor.sync_time(force=True)
                sync_duration = time.time() - sync_start
                
                results["sync_methods_working"][exchange] = {
                    "success": sync_success,
                    "duration": sync_duration
                }
                
                if sync_success:
                    print(f"  ✅ 时间同步成功 (耗时: {sync_duration:.2f}s)")
                    final_status = processor.get_sync_status()
                    print(f"  📊 同步后偏移: {final_status['time_offset_ms']}ms")
                else:
                    print(f"  ❌ 时间同步失败 (耗时: {sync_duration:.2f}s)")
                
                # 测试时间戳生成
                print(f"  🕐 测试时间戳生成...")
                test_timestamp = processor.get_synced_timestamp()
                current_time = int(time.time() * 1000)
                time_diff = abs(test_timestamp - current_time)
                
                results["timestamp_generation_test"][exchange] = {
                    "generated_timestamp": test_timestamp,
                    "current_time": current_time,
                    "time_diff": time_diff
                }
                
                print(f"  📊 生成时间戳: {test_timestamp}")
                print(f"  📊 当前时间戳: {current_time}")
                print(f"  📊 时间差: {time_diff}ms")
                
                if time_diff > 1000:
                    print(f"  ⚠️ 警告：时间差异超过1000ms阈值")
                    results["error_details"].append(
                        f"{exchange}: 时间戳生成异常，差异{time_diff}ms"
                    )
                
            except Exception as e:
                print(f"  ❌ {exchange} 处理器测试失败: {e}")
                results["error_details"].append(f"{exchange}: {str(e)}")
                
    except ImportError as e:
        print(f"❌ 统一时间戳处理器模块导入失败: {e}")
        results["error_details"].append(f"模块导入失败: {str(e)}")
    except Exception as e:
        print(f"❌ 时间戳处理器诊断异常: {e}")
        results["error_details"].append(f"诊断异常: {str(e)}")
    
    return results

async def simulate_cross_exchange_sync_validation():
    """模拟OpportunityScanner中的跨交易所时间戳同步验证"""
    print("🔍 模拟OpportunityScanner时间戳同步验证...")
    
    results = {
        "simulation_successful": False,
        "test_scenarios": [],
        "validation_logic_working": False,
        "threshold_analysis": {},
        "error_details": []
    }
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        # 模拟不同的时间戳差异场景
        test_scenarios = [
            {"name": "正常同步", "time_diff": 50, "expected_sync": True},
            {"name": "边界情况", "time_diff": 1000, "expected_sync": True},
            {"name": "轻微超标", "time_diff": 1100, "expected_sync": False},
            {"name": "严重超标", "time_diff": 5000, "expected_sync": False},
            {"name": "极端超标", "time_diff": 15000, "expected_sync": False}
        ]
        
        current_time = int(time.time() * 1000)
        
        for scenario in test_scenarios:
            print(f"\n🧪 测试场景: {scenario['name']} (时间差: {scenario['time_diff']}ms)")
            
            spot_timestamp = current_time
            futures_timestamp = current_time - scenario['time_diff']  # 期货时间戳更早
            
            scenario_result = {
                "scenario": scenario['name'],
                "time_diff": scenario['time_diff'],
                "expected_sync": scenario['expected_sync'],
                "actual_results": {}
            }
            
            # 测试不同交易所的验证结果
            exchanges = ["gate", "bybit", "okx"]
            all_consistent = True
            
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    
                    # 复制OpportunityScanner中的验证逻辑
                    is_synced, actual_time_diff = processor.validate_cross_exchange_sync(
                        spot_timestamp, futures_timestamp,
                        exchange, "futures_exchange",  # 模拟交易所组合
                        max_diff_ms=1000  # 使用相同的1000ms阈值
                    )
                    
                    scenario_result["actual_results"][exchange] = {
                        "is_synced": is_synced,
                        "actual_time_diff": actual_time_diff,
                        "matches_expected": is_synced == scenario['expected_sync']
                    }
                    
                    print(f"  {exchange}: is_synced={is_synced}, time_diff={actual_time_diff:.1f}ms")
                    
                    if is_synced != scenario['expected_sync']:
                        all_consistent = False
                        print(f"    ⚠️ 预期{scenario['expected_sync']}，实际{is_synced}")
                        
                except Exception as e:
                    print(f"  ❌ {exchange} 验证失败: {e}")
                    scenario_result["actual_results"][exchange] = {"error": str(e)}
                    all_consistent = False
            
            scenario_result["all_consistent"] = all_consistent
            results["test_scenarios"].append(scenario_result)
        
        # 分析阈值设置
        results["threshold_analysis"] = {
            "configured_threshold": 1000,
            "threshold_consistent": True,  # 代码中都使用1000ms
            "threshold_reasonable": True   # 1000ms是合理的阈值
        }
        
        results["validation_logic_working"] = len([s for s in results["test_scenarios"] if s["all_consistent"]]) > 0
        results["simulation_successful"] = True
        
    except Exception as e:
        print(f"❌ 跨交易所同步验证模拟失败: {e}")
        results["error_details"].append(f"模拟失败: {str(e)}")
    
    return results

async def compare_exchange_timestamps():
    """对比三个交易所的时间戳特征"""
    print("🔍 对比三个交易所时间戳特征...")
    
    results = {
        "comparison_successful": False,
        "exchange_characteristics": {},
        "timestamp_format_analysis": {},
        "potential_issues": [],
        "error_details": []
    }
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        exchanges = ["gate", "bybit", "okx"]
        current_time = time.time()
        
        for exchange in exchanges:
            print(f"\n🔍 分析 {exchange.upper()} 时间戳特征:")
            
            try:
                processor = get_timestamp_processor(exchange)
                
                # 测试多次时间戳生成，分析稳定性
                timestamps = []
                for i in range(5):
                    ts = processor.get_synced_timestamp()
                    timestamps.append(ts)
                    await asyncio.sleep(0.1)  # 100ms间隔
                
                # 分析时间戳特征
                timestamp_diffs = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]
                avg_interval = sum(timestamp_diffs) / len(timestamp_diffs) if timestamp_diffs else 0
                
                # 分析与系统时间的差异
                system_time_diffs = [abs(ts - int(time.time() * 1000)) for ts in timestamps]
                avg_system_diff = sum(system_time_diffs) / len(system_time_diffs)
                max_system_diff = max(system_time_diffs)
                
                characteristics = {
                    "sample_timestamps": timestamps,
                    "avg_generation_interval": avg_interval,
                    "avg_system_time_diff": avg_system_diff,
                    "max_system_time_diff": max_system_diff,
                    "timestamp_stability": max(timestamp_diffs) - min(timestamp_diffs) if timestamp_diffs else 0
                }
                
                results["exchange_characteristics"][exchange] = characteristics
                
                print(f"  📊 平均生成间隔: {avg_interval:.1f}ms")
                print(f"  📊 平均系统时差: {avg_system_diff:.1f}ms")
                print(f"  📊 最大系统时差: {max_system_diff:.1f}ms")
                print(f"  📊 时间戳稳定性: {characteristics['timestamp_stability']:.1f}ms")
                
                # 检查潜在问题
                if max_system_diff > 1000:
                    issue = f"{exchange}: 与系统时间差异过大({max_system_diff:.1f}ms)"
                    results["potential_issues"].append(issue)
                    print(f"  ⚠️ {issue}")
                
                if characteristics['timestamp_stability'] > 500:
                    issue = f"{exchange}: 时间戳生成不稳定({characteristics['timestamp_stability']:.1f}ms)"
                    results["potential_issues"].append(issue)
                    print(f"  ⚠️ {issue}")
                
            except Exception as e:
                print(f"  ❌ {exchange} 分析失败: {e}")
                results["error_details"].append(f"{exchange}: {str(e)}")
        
        # 分析时间戳格式
        results["timestamp_format_analysis"] = {
            "all_millisecond_format": True,
            "format_consistent": True,
            "notes": "所有交易所应该使用毫秒级时间戳"
        }
        
        results["comparison_successful"] = len(results["exchange_characteristics"]) > 0
        
    except Exception as e:
        print(f"❌ 交易所时间戳对比失败: {e}")
        results["error_details"].append(f"对比失败: {str(e)}")
    
    return results

async def test_websocket_timestamp_extraction():
    """测试实际WebSocket时间戳提取逻辑"""
    print("🔍 测试WebSocket时间戳提取逻辑...")
    
    results = {
        "extraction_test_successful": False,
        "mock_data_tests": {},
        "extraction_consistency": {},
        "potential_extraction_issues": [],
        "error_details": []
    }
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        # 模拟不同交易所的WebSocket数据格式
        mock_websocket_data = {
            "gate": [
                {"t": int(time.time() * 1000), "result": {"data": "test"}},
                {"result": {"t": int(time.time() * 1000 - 100)}},
                {}  # 空数据测试
            ],
            "bybit": [
                {"ts": int(time.time() * 1000), "data": "test"},
                {"ts": int(time.time() * 1000 - 200)},
                {}  # 空数据测试
            ],
            "okx": [
                {"ts": str(int(time.time() * 1000)), "data": "test"},
                {"ts": int(time.time() * 1000 - 300)},
                {}  # 空数据测试
            ]
        }
        
        for exchange, data_samples in mock_websocket_data.items():
            print(f"\n🧪 测试 {exchange.upper()} 时间戳提取:")
            
            processor = get_timestamp_processor(exchange)
            extraction_results = []
            
            for i, data in enumerate(data_samples):
                try:
                    extracted_timestamp = processor.get_synced_timestamp(data)
                    current_time = int(time.time() * 1000)
                    time_diff = abs(extracted_timestamp - current_time)
                    
                    result = {
                        "sample_index": i,
                        "raw_data": data,
                        "extracted_timestamp": extracted_timestamp,
                        "time_diff_from_current": time_diff,
                        "extraction_successful": True
                    }
                    
                    print(f"  样本{i}: 提取成功, 时间戳={extracted_timestamp}, 时差={time_diff:.1f}ms")
                    
                    if time_diff > 2000:  # 超过2秒认为异常
                        result["potential_issue"] = f"时间差异过大({time_diff:.1f}ms)"
                        results["potential_extraction_issues"].append(
                            f"{exchange}-样本{i}: {result['potential_issue']}"
                        )
                        print(f"    ⚠️ {result['potential_issue']}")
                    
                except Exception as e:
                    result = {
                        "sample_index": i,
                        "raw_data": data,
                        "extraction_successful": False,
                        "error": str(e)
                    }
                    print(f"  样本{i}: 提取失败 - {e}")
                
                extraction_results.append(result)
            
            results["mock_data_tests"][exchange] = extraction_results
        
        # 分析提取一致性
        successful_extractions = 0
        total_extractions = 0
        
        for exchange, extractions in results["mock_data_tests"].items():
            for extraction in extractions:
                total_extractions += 1
                if extraction.get("extraction_successful", False):
                    successful_extractions += 1
        
        results["extraction_consistency"] = {
            "success_rate": successful_extractions / total_extractions if total_extractions > 0 else 0,
            "total_tests": total_extractions,
            "successful_tests": successful_extractions
        }
        
        print(f"\n📊 时间戳提取成功率: {results['extraction_consistency']['success_rate']*100:.1f}%")
        
        results["extraction_test_successful"] = results["extraction_consistency"]["success_rate"] > 0.5
        
    except Exception as e:
        print(f"❌ WebSocket时间戳提取测试失败: {e}")
        results["error_details"].append(f"提取测试失败: {str(e)}")
    
    return results

def generate_diagnosis_report(processor_status, sync_validation, exchange_comparison, websocket_test):
    """生成综合诊断报告"""
    print("📋 生成诊断报告...")
    
    report = {
        "diagnosis_timestamp": datetime.now().isoformat(),
        "diagnosis_summary": {
            "overall_status": "UNKNOWN",
            "critical_issues": [],
            "potential_causes": [],
            "recommendations": []
        },
        "detailed_results": {
            "processor_status": processor_status,
            "sync_validation": sync_validation,
            "exchange_comparison": exchange_comparison,
            "websocket_test": websocket_test
        },
        "root_cause_analysis": {},
        "fix_priority": []
    }
    
    # 分析关键问题
    critical_issues = []
    potential_causes = []
    recommendations = []
    
    # 分析处理器状态
    if not processor_status.get("processors_available", False):
        critical_issues.append("统一时间戳处理器模块不可用")
        potential_causes.append("模块导入失败或代码错误")
        recommendations.append("检查统一时间戳处理器模块的导入和初始化")
    
    # 分析同步状态
    processor_statuses = processor_status.get("processors_status", {})
    unsync_exchanges = []
    large_offset_exchanges = []
    
    for exchange, status in processor_statuses.items():
        if not status.get("time_synced", False):
            unsync_exchanges.append(exchange)
        
        offset = status.get("time_offset_ms", 0)
        if abs(offset) > 2000:  # 超过2秒认为异常
            large_offset_exchanges.append(f"{exchange}({offset}ms)")
    
    if unsync_exchanges:
        critical_issues.append(f"交易所时间未同步: {', '.join(unsync_exchanges)}")
        potential_causes.append("交易所时间API访问失败或网络问题")
        recommendations.append("检查网络连接和交易所时间API的可访问性")
    
    if large_offset_exchanges:
        critical_issues.append(f"时间偏移异常: {', '.join(large_offset_exchanges)}")
        potential_causes.append("系统时间不准确或交易所时间API返回异常")
        recommendations.append("同步系统时间或检查交易所时间API")
    
    # 分析时间戳生成
    timestamp_tests = processor_status.get("timestamp_generation_test", {})
    problematic_generation = []
    
    for exchange, test in timestamp_tests.items():
        time_diff = test.get("time_diff", 0)
        if time_diff > 1000:
            problematic_generation.append(f"{exchange}({time_diff:.0f}ms)")
    
    if problematic_generation:
        critical_issues.append(f"时间戳生成异常: {', '.join(problematic_generation)}")
        potential_causes.append("时间戳处理器内部逻辑错误")
        recommendations.append("修复时间戳处理器的get_synced_timestamp方法")
    
    # 分析验证逻辑
    if not sync_validation.get("validation_logic_working", False):
        critical_issues.append("跨交易所时间戳同步验证逻辑异常")
        potential_causes.append("validate_cross_exchange_sync方法实现有问题")
        recommendations.append("检查并修复跨交易所时间戳同步验证逻辑")
    
    # 分析WebSocket提取
    extraction_success_rate = websocket_test.get("extraction_consistency", {}).get("success_rate", 0)
    if extraction_success_rate < 0.8:
        critical_issues.append(f"WebSocket时间戳提取成功率低({extraction_success_rate*100:.1f}%)")
        potential_causes.append("WebSocket数据格式处理有问题")
        recommendations.append("改进WebSocket时间戳提取逻辑")
    
    # 根因分析
    if len(critical_issues) == 0:
        report["diagnosis_summary"]["overall_status"] = "HEALTHY"
        potential_causes.append("系统可能正常工作，但需要进一步实时监控")
    elif len(critical_issues) <= 2:
        report["diagnosis_summary"]["overall_status"] = "NEEDS_ATTENTION"
    else:
        report["diagnosis_summary"]["overall_status"] = "CRITICAL"
    
    # 优先级排序
    if unsync_exchanges:
        report["fix_priority"].append("HIGH: 修复交易所时间同步问题")
    if problematic_generation:
        report["fix_priority"].append("HIGH: 修复时间戳生成异常")
    if extraction_success_rate < 0.8:
        report["fix_priority"].append("MEDIUM: 改进WebSocket时间戳提取")
    if not sync_validation.get("validation_logic_working", False):
        report["fix_priority"].append("MEDIUM: 检查验证逻辑")
    
    report["diagnosis_summary"]["critical_issues"] = critical_issues
    report["diagnosis_summary"]["potential_causes"] = potential_causes
    report["diagnosis_summary"]["recommendations"] = recommendations
    
    # 打印报告摘要
    print(f"\n📊 诊断结果摘要:")
    print(f"  总体状态: {report['diagnosis_summary']['overall_status']}")
    print(f"  关键问题数量: {len(critical_issues)}")
    
    if critical_issues:
        print(f"  关键问题:")
        for issue in critical_issues:
            print(f"    ❌ {issue}")
    
    if recommendations:
        print(f"  修复建议:")
        for rec in recommendations:
            print(f"    💡 {rec}")
    
    return report

def save_diagnosis_results(report):
    """保存诊断结果到文件"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"timestamp_sync_diagnosis_{timestamp}.json"
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 诊断报告已保存到: {filepath}")
        
        # 同时保存简化版的文本报告
        text_filename = f"timestamp_sync_diagnosis_{timestamp}.txt"
        text_filepath = os.path.join(os.path.dirname(__file__), text_filename)
        
        with open(text_filepath, 'w', encoding='utf-8') as f:
            f.write("时间戳同步问题诊断报告\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"诊断时间: {report['diagnosis_timestamp']}\n")
            f.write(f"总体状态: {report['diagnosis_summary']['overall_status']}\n\n")
            
            f.write("关键问题:\n")
            for issue in report['diagnosis_summary']['critical_issues']:
                f.write(f"  ❌ {issue}\n")
            f.write("\n")
            
            f.write("可能原因:\n")
            for cause in report['diagnosis_summary']['potential_causes']:
                f.write(f"  🔍 {cause}\n")
            f.write("\n")
            
            f.write("修复建议:\n")
            for rec in report['diagnosis_summary']['recommendations']:
                f.write(f"  💡 {rec}\n")
            f.write("\n")
            
            f.write("修复优先级:\n")
            for priority in report['fix_priority']:
                f.write(f"  📋 {priority}\n")
        
        print(f"💾 简化文本报告已保存到: {text_filepath}")
        
    except Exception as e:
        print(f"❌ 保存诊断结果失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())