#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ 机构级时间戳修复三段进阶验证测试
确保修复质量达到机构标准，零缺陷部署

验证机制：
① 基础核心测试：模块单元功能验证（参数输入输出、边界检查、错误处理）
② 复杂系统级联测试：模块交互逻辑、状态联动、多币种切换、多交易所分支
③ 生产测试：真实订单簿、真实API响应、网络波动模拟、多任务并发压力

所有测试必须100%通过，不容遗漏、不准掩盖！
"""

import asyncio
import sys
import os
import time
import json
import traceback
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstitutionalTimestampFixValidator:
    """🏛️ 机构级时间戳修复验证器"""
    
    def __init__(self):
        self.results = {
            "validation_timestamp": datetime.now().isoformat(),
            "validation_type": "institutional_grade_timestamp_fix_verification",
            "stage_1_basic_core": {"tests": [], "success_rate": 0.0, "critical_failures": []},
            "stage_2_system_cascade": {"tests": [], "success_rate": 0.0, "critical_failures": []},
            "stage_3_production": {"tests": [], "success_rate": 0.0, "critical_failures": []},
            "overall_grade": "PENDING",
            "deployment_ready": False,
            "critical_issues": [],
            "fix_quality_score": 0.0
        }
        
    def log_test_result(self, stage: str, test_name: str, success: bool, 
                       details: Dict, critical: bool = False, error_trace: str = None):
        """记录详细测试结果"""
        test_result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "critical": critical,
            "error_trace": error_trace,
            "timestamp": datetime.now().isoformat()
        }
        
        self.results[stage]["tests"].append(test_result)
        
        if not success:
            if critical:
                self.results["critical_issues"].append({
                    "stage": stage,
                    "test": test_name,
                    "issue": details,
                    "error_trace": error_trace
                })
                self.results[stage]["critical_failures"].append(test_name)
            
            # 实时输出
            status = "❌ CRITICAL FAIL" if critical else "⚠️ FAIL"
            print(f"{status} [{stage.upper()}] {test_name}")
            if error_trace:
                print(f"   错误堆栈: {error_trace[:200]}...")
            print(f"   详情: {details}")
        else:
            print(f"✅ [{stage.upper()}] {test_name}")

    # ==================== 阶段1：基础核心测试 ====================
    
    async def stage_1_basic_core_tests(self):
        """🏛️ 阶段1：基础核心测试 - 模块单元功能验证"""
        print("\n🏛️ === 阶段1：基础核心测试 ===")
        print("验证修复点本身100%稳定：参数输入输出、边界检查、错误处理")
        
        # 测试1：修复后ArbitrageEngine模块完整性
        await self._test_1_arbitrage_engine_integrity()
        
        # 测试2：统一模块接口调用正确性
        await self._test_2_unified_module_interface()
        
        # 测试3：时间戳同步核心功能
        await self._test_3_timestamp_sync_core()
        
        # 测试4：错误处理和边界条件
        await self._test_4_error_handling_boundaries()
        
        # 测试5：接口兼容性验证
        await self._test_5_interface_compatibility()
        
        # 计算阶段成功率
        total_tests = len(self.results["stage_1_basic_core"]["tests"])
        passed_tests = sum(1 for t in self.results["stage_1_basic_core"]["tests"] if t["success"])
        self.results["stage_1_basic_core"]["success_rate"] = passed_tests / total_tests if total_tests > 0 else 0
        
        print(f"\n📊 阶段1成功率: {self.results['stage_1_basic_core']['success_rate']*100:.1f}% ({passed_tests}/{total_tests})")

    async def _test_1_arbitrage_engine_integrity(self):
        """测试1：修复后ArbitrageEngine模块完整性"""
        test_name = "arbitrage_engine_integrity"
        try:
            # 导入测试
            from core.arbitrage_engine import ArbitrageEngine
            
            # 创建实例测试
            engine = ArbitrageEngine()
            
            # 验证关键方法存在
            required_methods = [
                '_initialize_timestamp_sync',
                '_validate_cross_exchange_timestamp_consistency',
                '_setup_timestamp_monitoring',
                '_check_timestamp_sync_health'
            ]
            
            missing_methods = []
            for method in required_methods:
                if not hasattr(engine, method):
                    missing_methods.append(method)
            
            # 验证方法签名正确
            import inspect
            sync_method = getattr(engine, '_initialize_timestamp_sync')
            if not inspect.iscoroutinefunction(sync_method):
                missing_methods.append("_initialize_timestamp_sync (not async)")
            
            details = {
                "engine_created": True,
                "required_methods_present": len(missing_methods) == 0,
                "missing_methods": missing_methods,
                "method_count": len(required_methods) - len(missing_methods)
            }
            
            success = len(missing_methods) == 0
            self.log_test_result("stage_1_basic_core", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "import_failed": True}
            self.log_test_result("stage_1_basic_core", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    async def _test_2_unified_module_interface(self):
        """测试2：统一模块接口调用正确性"""
        test_name = "unified_module_interface"
        try:
            # 测试统一模块导入
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            # 测试函数签名
            import inspect
            sig = inspect.signature(initialize_all_timestamp_processors)
            params = list(sig.parameters.keys())
            
            # 验证接口正确性
            has_force_sync = 'force_sync' in params
            is_coroutine = inspect.iscoroutinefunction(initialize_all_timestamp_processors)
            
            # 测试调用（快速测试）
            call_success = False
            call_result = None
            try:
                call_result = await initialize_all_timestamp_processors(force_sync=False)
                call_success = isinstance(call_result, dict)
            except Exception as call_e:
                call_success = False
                call_result = str(call_e)
            
            details = {
                "import_success": True,
                "has_force_sync_param": has_force_sync,
                "is_async_function": is_coroutine,
                "call_success": call_success,
                "call_result_type": type(call_result).__name__,
                "parameters": params
            }
            
            success = has_force_sync and is_coroutine and call_success
            self.log_test_result("stage_1_basic_core", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "interface_test_failed": True}
            self.log_test_result("stage_1_basic_core", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    async def _test_3_timestamp_sync_core(self):
        """测试3：时间戳同步核心功能"""
        test_name = "timestamp_sync_core"
        try:
            from websocket.unified_timestamp_processor import (
                initialize_all_timestamp_processors, 
                get_timestamp_processor
            )
            
            # 测试强制同步
            sync_start = time.time()
            sync_results = await initialize_all_timestamp_processors(force_sync=True)
            sync_duration = time.time() - sync_start
            
            # 验证同步结果
            expected_exchanges = {"gate", "bybit", "okx"}
            actual_exchanges = set(sync_results.keys())
            missing_exchanges = expected_exchanges - actual_exchanges
            extra_exchanges = actual_exchanges - expected_exchanges
            
            # 检查各交易所同步状态
            sync_statuses = {}
            for exchange in expected_exchanges:
                if exchange in sync_results:
                    try:
                        processor = get_timestamp_processor(exchange)
                        status = processor.get_sync_status()
                        sync_statuses[exchange] = {
                            "sync_result": sync_results[exchange],
                            "processor_status": status.get("time_synced", False),
                            "offset_ms": status.get("time_offset_ms", 0)
                        }
                    except Exception as e:
                        sync_statuses[exchange] = {"error": str(e)}
            
            success_count = sum(1 for result in sync_results.values() if result)
            
            details = {
                "sync_duration": sync_duration,
                "expected_exchanges": list(expected_exchanges),
                "actual_exchanges": list(actual_exchanges), 
                "missing_exchanges": list(missing_exchanges),
                "extra_exchanges": list(extra_exchanges),
                "success_count": success_count,
                "total_count": len(sync_results),
                "sync_statuses": sync_statuses,
                "sync_results": sync_results
            }
            
            success = (
                len(missing_exchanges) == 0 and
                len(extra_exchanges) == 0 and
                success_count >= 2 and  # 至少2个交易所成功
                sync_duration < 10.0    # 10秒内完成
            )
            
            self.log_test_result("stage_1_basic_core", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "sync_test_failed": True}
            self.log_test_result("stage_1_basic_core", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    async def _test_4_error_handling_boundaries(self):
        """测试4：错误处理和边界条件"""
        test_name = "error_handling_boundaries"
        try:
            from core.arbitrage_engine import ArbitrageEngine
            
            engine = ArbitrageEngine()
            
            # 测试网络异常情况下的行为
            network_error_handled = True
            try:
                # 模拟网络错误（通过传入无效配置）
                result = await engine._initialize_timestamp_sync()
                # 应该返回空字典或具有错误处理
                if not isinstance(result, dict):
                    network_error_handled = False
            except Exception as e:
                # 异常应该被捕获，不应该传播到这里
                network_error_handled = False
            
            # 测试健康检查的边界条件
            health_check_handled = True
            try:
                await engine._check_timestamp_sync_health()
            except Exception as e:
                health_check_handled = False
            
            # 测试一致性验证的边界条件
            consistency_check_handled = True  
            try:
                await engine._validate_cross_exchange_timestamp_consistency()
            except Exception as e:
                consistency_check_handled = False
            
            details = {
                "network_error_handled": network_error_handled,
                "health_check_handled": health_check_handled,
                "consistency_check_handled": consistency_check_handled
            }
            
            success = network_error_handled and health_check_handled and consistency_check_handled
            self.log_test_result("stage_1_basic_core", test_name, success, details, critical=False)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "boundary_test_failed": True}
            self.log_test_result("stage_1_basic_core", test_name, False, details, 
                               critical=False, error_trace=error_trace)

    async def _test_5_interface_compatibility(self):
        """测试5：接口兼容性验证"""
        test_name = "interface_compatibility"
        try:
            # 测试ArbitrageEngine中的调用是否与统一模块兼容
            from core.arbitrage_engine import ArbitrageEngine
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            # 创建引擎实例
            engine = ArbitrageEngine()
            
            # 测试引擎内部调用
            engine_result = await engine._initialize_timestamp_sync()
            
            # 测试直接调用统一模块
            direct_result = await initialize_all_timestamp_processors(force_sync=True)
            
            # 比较结果结构
            engine_result_valid = isinstance(engine_result, dict)
            direct_result_valid = isinstance(direct_result, dict)
            
            # 检查结果一致性（至少在结构上）
            structure_compatible = True
            if engine_result_valid and direct_result_valid:
                # 检查键集合是否一致
                engine_keys = set(engine_result.keys()) if engine_result else set()
                direct_keys = set(direct_result.keys())
                structure_compatible = engine_keys.issubset(direct_keys) or direct_keys.issubset(engine_keys)
            
            details = {
                "engine_call_success": engine_result_valid,
                "direct_call_success": direct_result_valid,
                "structure_compatible": structure_compatible,
                "engine_result_keys": list(engine_result.keys()) if engine_result_valid else [],
                "direct_result_keys": list(direct_result.keys()) if direct_result_valid else []
            }
            
            success = engine_result_valid and direct_result_valid and structure_compatible
            self.log_test_result("stage_1_basic_core", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "compatibility_test_failed": True}
            self.log_test_result("stage_1_basic_core", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    # ==================== 阶段2：复杂系统级联测试 ====================
    
    async def stage_2_system_cascade_tests(self):
        """🏛️ 阶段2：复杂系统级联测试 - 模块交互逻辑验证"""
        print("\n🏛️ === 阶段2：复杂系统级联测试 ===") 
        print("验证系统协同一致性：模块交互、状态联动、多交易所分支")
        
        # 测试1：ArbitrageEngine启动流程集成
        await self._test_6_arbitrage_engine_startup_integration()
        
        # 测试2：跨交易所时间戳一致性验证
        await self._test_7_cross_exchange_consistency()
        
        # 测试3：监控系统联动测试
        await self._test_8_monitoring_system_cascade()
        
        # 测试4：多交易所状态管理
        await self._test_9_multi_exchange_state_management()
        
        # 测试5：系统恢复和重同步联动
        await self._test_10_system_recovery_cascade()
        
        # 计算阶段成功率
        total_tests = len(self.results["stage_2_system_cascade"]["tests"])
        passed_tests = sum(1 for t in self.results["stage_2_system_cascade"]["tests"] if t["success"])
        self.results["stage_2_system_cascade"]["success_rate"] = passed_tests / total_tests if total_tests > 0 else 0
        
        print(f"\n📊 阶段2成功率: {self.results['stage_2_system_cascade']['success_rate']*100:.1f}% ({passed_tests}/{total_tests})")

    async def _test_6_arbitrage_engine_startup_integration(self):
        """测试6：ArbitrageEngine启动流程集成"""
        test_name = "arbitrage_engine_startup_integration"
        try:
            from core.arbitrage_engine import ArbitrageEngine
            
            # 创建引擎并模拟启动流程
            engine = ArbitrageEngine()
            
            # 测试初始化组件方法是否调用时间戳同步
            startup_success = False
            sync_called = False
            
            try:
                # 模拟启动时的时间戳同步调用
                sync_result = await engine._initialize_timestamp_sync()
                sync_called = True
                startup_success = isinstance(sync_result, dict)
            except Exception as e:
                startup_success = False
            
            # 验证监控任务是否设置
            monitoring_setup = hasattr(engine, '_setup_timestamp_monitoring')
            
            # 测试健康检查是否工作
            health_check_works = False
            try:
                await engine._check_timestamp_sync_health()
                health_check_works = True
            except Exception as e:
                health_check_works = False
            
            details = {
                "engine_created": True,
                "sync_called": sync_called,
                "startup_success": startup_success,
                "monitoring_setup_available": monitoring_setup,
                "health_check_works": health_check_works
            }
            
            success = sync_called and startup_success and health_check_works
            self.log_test_result("stage_2_system_cascade", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "startup_integration_failed": True}
            self.log_test_result("stage_2_system_cascade", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    async def _test_7_cross_exchange_consistency(self):
        """测试7：跨交易所时间戳一致性验证"""
        test_name = "cross_exchange_consistency"
        try:
            from websocket.unified_timestamp_processor import (
                initialize_all_timestamp_processors,
                get_timestamp_processor
            )
            
            # 强制同步所有交易所
            await initialize_all_timestamp_processors(force_sync=True)
            
            # 获取所有交易所的时间戳
            exchanges = ["gate", "bybit", "okx"]
            timestamps = {}
            offsets = {}
            
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    timestamp = processor.get_synced_timestamp()
                    status = processor.get_sync_status()
                    
                    timestamps[exchange] = timestamp
                    offsets[exchange] = status.get("time_offset_ms", 0)
                except Exception as e:
                    timestamps[exchange] = None
                    offsets[exchange] = None
            
            # 计算时间戳差异
            valid_timestamps = [ts for ts in timestamps.values() if ts is not None]
            if len(valid_timestamps) >= 2:
                max_diff = max(valid_timestamps) - min(valid_timestamps)
                avg_timestamp = sum(valid_timestamps) / len(valid_timestamps)
                within_threshold = max_diff <= 1000  # 1000ms阈值
            else:
                max_diff = float('inf')
                avg_timestamp = 0
                within_threshold = False
            
            # 验证OpportunityScanner的验证逻辑
            validation_logic_test = True
            try:
                if len(valid_timestamps) >= 2:
                    # 测试边界情况
                    processor = get_timestamp_processor("gate")
                    current_time = int(time.time() * 1000)
                    
                    # 测试1000ms边界
                    is_synced_boundary, diff_boundary = processor.validate_cross_exchange_sync(
                        current_time, current_time - 1000, "gate", "bybit", max_diff_ms=1000
                    )
                    
                    # 测试1100ms超标
                    is_synced_over, diff_over = processor.validate_cross_exchange_sync(
                        current_time, current_time - 1100, "gate", "bybit", max_diff_ms=1000
                    )
                    
                    # 边界应该通过，超标应该失败
                    validation_logic_test = is_synced_boundary and not is_synced_over
            except Exception as e:
                validation_logic_test = False
            
            details = {
                "timestamps": timestamps,
                "offsets": offsets,
                "valid_timestamp_count": len(valid_timestamps),
                "max_difference_ms": max_diff,
                "within_threshold": within_threshold,
                "average_timestamp": avg_timestamp,
                "validation_logic_test": validation_logic_test
            }
            
            success = within_threshold and validation_logic_test and len(valid_timestamps) >= 2
            self.log_test_result("stage_2_system_cascade", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "consistency_test_failed": True}
            self.log_test_result("stage_2_system_cascade", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    async def _test_8_monitoring_system_cascade(self):
        """测试8：监控系统联动测试"""
        test_name = "monitoring_system_cascade"
        try:
            from core.arbitrage_engine import ArbitrageEngine
            
            # 创建引擎实例
            engine = ArbitrageEngine()
            
            # 测试监控任务设置
            engine._setup_timestamp_monitoring()
            
            # 测试健康检查功能
            health_check_success = True
            try:
                await engine._check_timestamp_sync_health()
            except Exception as e:
                health_check_success = False
            
            # 测试一致性验证功能
            consistency_check_success = True
            try:
                await engine._validate_cross_exchange_timestamp_consistency()
            except Exception as e:
                consistency_check_success = False
            
            # 测试监控任务是否会触发重同步
            resync_trigger_available = True
            try:
                # 检查重同步逻辑是否存在
                import inspect
                source = inspect.getsource(engine._check_timestamp_sync_health)
                resync_trigger_available = "initialize_all_timestamp_processors" in source
            except Exception as e:
                resync_trigger_available = False
            
            details = {
                "monitoring_setup_success": True,
                "health_check_success": health_check_success,
                "consistency_check_success": consistency_check_success,
                "resync_trigger_available": resync_trigger_available
            }
            
            success = health_check_success and consistency_check_success and resync_trigger_available
            self.log_test_result("stage_2_system_cascade", test_name, success, details, critical=False)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "monitoring_cascade_failed": True}
            self.log_test_result("stage_2_system_cascade", test_name, False, details, 
                               critical=False, error_trace=error_trace)

    async def _test_9_multi_exchange_state_management(self):
        """测试9：多交易所状态管理"""
        test_name = "multi_exchange_state_management"
        try:
            from websocket.unified_timestamp_processor import (
                initialize_all_timestamp_processors,
                get_timestamp_processor
            )
            
            # 初始化所有交易所
            init_results = await initialize_all_timestamp_processors(force_sync=True)
            
            # 验证每个交易所的状态独立性
            exchanges = ["gate", "bybit", "okx"]
            state_independence = True
            processor_states = {}
            
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    status = processor.get_sync_status()
                    
                    processor_states[exchange] = {
                        "time_synced": status.get("time_synced", False),
                        "time_offset_ms": status.get("time_offset_ms", 0),
                        "last_sync_time": status.get("last_sync_time", 0)
                    }
                    
                    # 每个处理器应该有独立的状态
                    if processor_states[exchange]["last_sync_time"] == 0:
                        state_independence = False
                        
                except Exception as e:
                    processor_states[exchange] = {"error": str(e)}
                    state_independence = False
            
            # 测试状态持久性
            state_persistence = True
            try:
                # 获取初始状态
                initial_states = {}
                for exchange in exchanges:
                    processor = get_timestamp_processor(exchange)
                    initial_states[exchange] = processor.get_sync_status()
                
                # 等待短暂时间
                await asyncio.sleep(0.1)
                
                # 再次获取状态，应该保持一致
                for exchange in exchanges:
                    processor = get_timestamp_processor(exchange)
                    current_state = processor.get_sync_status()
                    if current_state.get("time_synced") != initial_states[exchange].get("time_synced"):
                        state_persistence = False
                        break
                        
            except Exception as e:
                state_persistence = False
            
            details = {
                "init_results": init_results,
                "processor_states": processor_states,
                "state_independence": state_independence,
                "state_persistence": state_persistence,
                "total_processors": len(exchanges)
            }
            
            success = state_independence and state_persistence and len(init_results) == len(exchanges)
            self.log_test_result("stage_2_system_cascade", test_name, success, details, critical=False)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "state_management_failed": True}
            self.log_test_result("stage_2_system_cascade", test_name, False, details, 
                               critical=False, error_trace=error_trace)

    async def _test_10_system_recovery_cascade(self):
        """测试10：系统恢复和重同步联动"""
        test_name = "system_recovery_cascade"
        try:
            from core.arbitrage_engine import ArbitrageEngine
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            # 创建引擎实例
            engine = ArbitrageEngine()
            
            # 初始同步
            initial_sync = await initialize_all_timestamp_processors(force_sync=True)
            
            # 模拟系统恢复场景
            recovery_success = True
            try:
                # 调用健康检查，应该触发重同步逻辑
                await engine._check_timestamp_sync_health()
                
                # 再次检查时间戳一致性
                await engine._validate_cross_exchange_timestamp_consistency()
                
            except Exception as e:
                recovery_success = False
            
            # 测试重同步功能
            resync_success = True
            try:
                # 强制重新同步
                resync_results = await initialize_all_timestamp_processors(force_sync=True)
                resync_success = isinstance(resync_results, dict) and len(resync_results) > 0
            except Exception as e:
                resync_success = False
            
            # 测试系统级联恢复
            cascade_recovery = True
            try:
                # 模拟多次恢复操作
                for i in range(3):
                    await engine._check_timestamp_sync_health()
                    await asyncio.sleep(0.1)
            except Exception as e:
                cascade_recovery = False
            
            details = {
                "initial_sync_success": isinstance(initial_sync, dict),
                "recovery_success": recovery_success,
                "resync_success": resync_success,
                "cascade_recovery": cascade_recovery,
                "initial_sync_results": initial_sync
            }
            
            success = recovery_success and resync_success and cascade_recovery
            self.log_test_result("stage_2_system_cascade", test_name, success, details, critical=False)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "recovery_cascade_failed": True}
            self.log_test_result("stage_2_system_cascade", test_name, False, details, 
                               critical=False, error_trace=error_trace)

    # ==================== 阶段3：生产测试 ====================
    
    async def stage_3_production_tests(self):
        """🏛️ 阶段3：生产测试 - 真实环境模拟"""
        print("\n🏛️ === 阶段3：生产测试 ===")
        print("真实环境验证：真实API响应、网络波动、并发压力、极限场景")
        
        # 测试1：真实API响应测试
        await self._test_11_real_api_response()
        
        # 测试2：网络波动模拟
        await self._test_12_network_fluctuation_simulation()
        
        # 测试3：并发压力测试
        await self._test_13_concurrent_pressure_test()
        
        # 测试4：极限场景回放
        await self._test_14_extreme_scenario_replay()
        
        # 测试5：生产环境集成测试
        await self._test_15_production_integration()
        
        # 计算阶段成功率
        total_tests = len(self.results["stage_3_production"]["tests"])
        passed_tests = sum(1 for t in self.results["stage_3_production"]["tests"] if t["success"])
        self.results["stage_3_production"]["success_rate"] = passed_tests / total_tests if total_tests > 0 else 0
        
        print(f"\n📊 阶段3成功率: {self.results['stage_3_production']['success_rate']*100:.1f}% ({passed_tests}/{total_tests})")

    async def _test_11_real_api_response(self):
        """测试11：真实API响应测试"""
        test_name = "real_api_response"
        try:
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            # 真实API调用测试
            api_start_time = time.time()
            sync_results = await initialize_all_timestamp_processors(force_sync=True)
            api_duration = time.time() - api_start_time
            
            # 验证真实API响应
            real_api_success = True
            api_response_times = {}
            api_errors = []
            
            for exchange, success in sync_results.items():
                if not success:
                    api_errors.append(f"{exchange}: sync failed")
                    real_api_success = False
            
            # 性能基准测试
            performance_acceptable = api_duration < 15.0  # 15秒内完成真实API调用
            
            # 测试API响应数据完整性
            data_completeness = len(sync_results) == 3  # 应该有3个交易所的结果
            
            details = {
                "api_call_duration": api_duration,
                "sync_results": sync_results,
                "real_api_success": real_api_success,
                "performance_acceptable": performance_acceptable,
                "data_completeness": data_completeness,
                "api_errors": api_errors
            }
            
            success = real_api_success and performance_acceptable and data_completeness
            self.log_test_result("stage_3_production", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "real_api_test_failed": True}
            self.log_test_result("stage_3_production", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    async def _test_12_network_fluctuation_simulation(self):
        """测试12：网络波动模拟"""
        test_name = "network_fluctuation_simulation"
        try:
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            # 模拟网络延迟情况下的行为
            fluctuation_results = []
            
            for delay in [0.1, 0.5, 1.0]:  # 模拟不同网络延迟
                test_start = time.time()
                
                try:
                    # 添加模拟延迟
                    await asyncio.sleep(delay)
                    sync_result = await initialize_all_timestamp_processors(force_sync=True)
                    test_duration = time.time() - test_start
                    
                    fluctuation_results.append({
                        "delay": delay,
                        "sync_success": isinstance(sync_result, dict),
                        "test_duration": test_duration,
                        "sync_result": sync_result
                    })
                    
                except Exception as e:
                    fluctuation_results.append({
                        "delay": delay,
                        "sync_success": False,
                        "error": str(e)
                    })
                
                # 测试间隔
                await asyncio.sleep(0.5)
            
            # 分析波动测试结果
            successful_tests = sum(1 for r in fluctuation_results if r.get("sync_success", False))
            fluctuation_resilience = successful_tests >= 2  # 至少2/3成功
            
            # 测试超时处理
            timeout_handled = True
            try:
                # 使用短超时测试
                timeout_task = asyncio.wait_for(
                    initialize_all_timestamp_processors(force_sync=True),
                    timeout=20.0  # 20秒超时
                )
                await timeout_task
            except asyncio.TimeoutError:
                timeout_handled = False
            except Exception as e:
                # 其他异常也算超时处理成功（被正确捕获）
                pass
            
            details = {
                "fluctuation_results": fluctuation_results,
                "successful_tests": successful_tests,
                "total_tests": len(fluctuation_results),
                "fluctuation_resilience": fluctuation_resilience,
                "timeout_handled": timeout_handled
            }
            
            success = fluctuation_resilience and timeout_handled
            self.log_test_result("stage_3_production", test_name, success, details, critical=False)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "network_simulation_failed": True}
            self.log_test_result("stage_3_production", test_name, False, details, 
                               critical=False, error_trace=error_trace)

    async def _test_13_concurrent_pressure_test(self):
        """测试13：并发压力测试"""
        test_name = "concurrent_pressure_test"
        try:
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            # 并发调用测试
            concurrent_tasks = []
            task_count = 5  # 5个并发任务
            
            for i in range(task_count):
                task = initialize_all_timestamp_processors(force_sync=False)  # 避免过度同步
                concurrent_tasks.append(task)
            
            # 执行并发测试
            pressure_start = time.time()
            concurrent_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
            pressure_duration = time.time() - pressure_start
            
            # 分析并发结果
            successful_calls = 0
            failed_calls = 0
            exceptions = []
            
            for i, result in enumerate(concurrent_results):
                if isinstance(result, Exception):
                    failed_calls += 1
                    exceptions.append(f"Task{i}: {str(result)}")
                elif isinstance(result, dict):
                    successful_calls += 1
                else:
                    failed_calls += 1
            
            # 并发性能评估
            concurrency_performance = pressure_duration < 30.0  # 30秒内完成
            concurrency_stability = successful_calls >= task_count * 0.6  # 至少60%成功
            
            # 测试系统状态一致性
            state_consistency = True
            try:
                # 并发后检查系统状态
                from websocket.unified_timestamp_processor import get_timestamp_processor
                for exchange in ["gate", "bybit", "okx"]:
                    processor = get_timestamp_processor(exchange)
                    status = processor.get_sync_status()
                    if not isinstance(status, dict):
                        state_consistency = False
                        break
            except Exception as e:
                state_consistency = False
            
            details = {
                "task_count": task_count,
                "pressure_duration": pressure_duration,
                "successful_calls": successful_calls,
                "failed_calls": failed_calls,
                "exceptions": exceptions,
                "concurrency_performance": concurrency_performance,
                "concurrency_stability": concurrency_stability,
                "state_consistency": state_consistency
            }
            
            success = concurrency_performance and concurrency_stability and state_consistency
            self.log_test_result("stage_3_production", test_name, success, details, critical=False)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "pressure_test_failed": True}
            self.log_test_result("stage_3_production", test_name, False, details, 
                               critical=False, error_trace=error_trace)

    async def _test_14_extreme_scenario_replay(self):
        """测试14：极限场景回放"""
        test_name = "extreme_scenario_replay"
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            from core.arbitrage_engine import ArbitrageEngine
            
            # 模拟原始问题场景：大时间差异
            extreme_scenarios = [
                {"name": "轻微超标", "time_diff": 1100},
                {"name": "严重超标", "time_diff": 5000},
                {"name": "极端超标", "time_diff": 15000}
            ]
            
            scenario_results = []
            
            for scenario in extreme_scenarios:
                try:
                    # 模拟跨交易所验证
                    processor = get_timestamp_processor("gate")
                    current_time = int(time.time() * 1000)
                    
                    spot_timestamp = current_time
                    futures_timestamp = current_time - scenario["time_diff"]
                    
                    is_synced, actual_diff = processor.validate_cross_exchange_sync(
                        spot_timestamp, futures_timestamp,
                        "gate", "bybit",
                        max_diff_ms=1000
                    )
                    
                    # 验证逻辑是否正确：超过1000ms应该返回False
                    expected_result = scenario["time_diff"] <= 1000
                    logic_correct = is_synced == expected_result
                    
                    scenario_results.append({
                        "scenario": scenario["name"],
                        "time_diff": scenario["time_diff"],
                        "is_synced": is_synced,
                        "actual_diff": actual_diff,
                        "expected_result": expected_result,
                        "logic_correct": logic_correct
                    })
                    
                except Exception as e:
                    scenario_results.append({
                        "scenario": scenario["name"],
                        "error": str(e),
                        "logic_correct": False
                    })
            
            # 测试ArbitrageEngine在极限场景下的表现
            engine_extreme_test = True
            try:
                engine = ArbitrageEngine()
                await engine._initialize_timestamp_sync()
                await engine._check_timestamp_sync_health()
                await engine._validate_cross_exchange_timestamp_consistency()
            except Exception as e:
                engine_extreme_test = False
            
            # 分析极限场景结果
            correct_logic_count = sum(1 for r in scenario_results if r.get("logic_correct", False))
            extreme_handling = correct_logic_count == len(extreme_scenarios)
            
            details = {
                "scenario_results": scenario_results,
                "correct_logic_count": correct_logic_count,
                "total_scenarios": len(extreme_scenarios),
                "extreme_handling": extreme_handling,
                "engine_extreme_test": engine_extreme_test
            }
            
            success = extreme_handling and engine_extreme_test
            self.log_test_result("stage_3_production", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "extreme_scenario_failed": True}
            self.log_test_result("stage_3_production", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    async def _test_15_production_integration(self):
        """测试15：生产环境集成测试"""
        test_name = "production_integration"
        try:
            from core.arbitrage_engine import ArbitrageEngine
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            # 完整的生产流程测试
            integration_steps = []
            
            # 步骤1：系统初始化
            try:
                engine = ArbitrageEngine()
                integration_steps.append({"step": "engine_creation", "success": True})
            except Exception as e:
                integration_steps.append({"step": "engine_creation", "success": False, "error": str(e)})
            
            # 步骤2：时间戳同步
            try:
                sync_result = await engine._initialize_timestamp_sync()
                sync_success = isinstance(sync_result, dict) and len(sync_result) > 0
                integration_steps.append({"step": "timestamp_sync", "success": sync_success})
            except Exception as e:
                integration_steps.append({"step": "timestamp_sync", "success": False, "error": str(e)})
            
            # 步骤3：一致性验证
            try:
                await engine._validate_cross_exchange_timestamp_consistency()
                integration_steps.append({"step": "consistency_check", "success": True})
            except Exception as e:
                integration_steps.append({"step": "consistency_check", "success": False, "error": str(e)})
            
            # 步骤4：监控系统
            try:
                await engine._check_timestamp_sync_health()
                integration_steps.append({"step": "health_monitoring", "success": True})
            except Exception as e:
                integration_steps.append({"step": "health_monitoring", "success": False, "error": str(e)})
            
            # 步骤5：重同步测试
            try:
                resync_result = await initialize_all_timestamp_processors(force_sync=True)
                resync_success = isinstance(resync_result, dict)
                integration_steps.append({"step": "resync_capability", "success": resync_success})
            except Exception as e:
                integration_steps.append({"step": "resync_capability", "success": False, "error": str(e)})
            
            # 分析集成测试结果
            successful_steps = sum(1 for step in integration_steps if step["success"])
            integration_success_rate = successful_steps / len(integration_steps)
            production_ready = integration_success_rate >= 0.8  # 80%以上成功
            
            # 关键步骤检查
            critical_steps = ["engine_creation", "timestamp_sync", "consistency_check"]
            critical_success = all(
                any(step["step"] == critical and step["success"] for step in integration_steps)
                for critical in critical_steps
            )
            
            details = {
                "integration_steps": integration_steps,
                "successful_steps": successful_steps,
                "total_steps": len(integration_steps),
                "integration_success_rate": integration_success_rate,
                "production_ready": production_ready,
                "critical_success": critical_success
            }
            
            success = production_ready and critical_success
            self.log_test_result("stage_3_production", test_name, success, details, critical=True)
            
        except Exception as e:
            error_trace = traceback.format_exc()
            details = {"error": str(e), "production_integration_failed": True}
            self.log_test_result("stage_3_production", test_name, False, details, 
                               critical=True, error_trace=error_trace)

    # ==================== 综合评估和报告生成 ====================
    
    def generate_final_assessment(self):
        """生成最终评估报告"""
        print("\n🏛️ === 最终评估 ===")
        
        # 计算总体成功率
        all_tests = []
        all_tests.extend(self.results["stage_1_basic_core"]["tests"])
        all_tests.extend(self.results["stage_2_system_cascade"]["tests"])
        all_tests.extend(self.results["stage_3_production"]["tests"])
        
        total_tests = len(all_tests)
        passed_tests = sum(1 for test in all_tests if test["success"])
        overall_success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        # 计算关键测试成功率
        critical_tests = [test for test in all_tests if test["critical"]]
        critical_passed = sum(1 for test in critical_tests if test["success"])
        critical_success_rate = critical_passed / len(critical_tests) if critical_tests else 0
        
        # 计算修复质量分数
        stage1_weight = 0.4
        stage2_weight = 0.3
        stage3_weight = 0.3
        
        fix_quality_score = (
            self.results["stage_1_basic_core"]["success_rate"] * stage1_weight +
            self.results["stage_2_system_cascade"]["success_rate"] * stage2_weight +
            self.results["stage_3_production"]["success_rate"] * stage3_weight
        )
        
        # 确定整体等级
        if fix_quality_score >= 0.95 and critical_success_rate >= 0.9:
            overall_grade = "EXCELLENT"
            deployment_ready = True
        elif fix_quality_score >= 0.85 and critical_success_rate >= 0.8:
            overall_grade = "GOOD"
            deployment_ready = True
        elif fix_quality_score >= 0.70 and critical_success_rate >= 0.7:
            overall_grade = "ACCEPTABLE"
            deployment_ready = False
        else:
            overall_grade = "NEEDS_IMPROVEMENT"
            deployment_ready = False
        
        # 更新结果
        self.results["overall_grade"] = overall_grade
        self.results["deployment_ready"] = deployment_ready
        self.results["fix_quality_score"] = fix_quality_score
        
        # 打印最终报告
        print(f"📊 总体测试结果:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   成功率: {overall_success_rate*100:.1f}%")
        print(f"   关键测试成功率: {critical_success_rate*100:.1f}%")
        print(f"   修复质量分数: {fix_quality_score*100:.1f}%")
        print(f"   整体等级: {overall_grade}")
        print(f"   部署就绪: {'是' if deployment_ready else '否'}")
        
        if self.results["critical_issues"]:
            print(f"\n❌ 关键问题 ({len(self.results['critical_issues'])}):")
            for issue in self.results["critical_issues"]:
                print(f"   - [{issue['stage']}] {issue['test']}: {issue['issue']}")
        
        return self.results

async def main():
    """主验证流程"""
    print("🏛️ 机构级时间戳修复三段进阶验证测试")
    print("=" * 80)
    print("确保修复质量达到机构标准，零缺陷部署")
    print("所有测试必须100%通过，不容遗漏、不准掩盖！")
    
    validator = InstitutionalTimestampFixValidator()
    
    try:
        # 阶段1：基础核心测试
        await validator.stage_1_basic_core_tests()
        
        # 阶段2：复杂系统级联测试  
        await validator.stage_2_system_cascade_tests()
        
        # 阶段3：生产测试
        await validator.stage_3_production_tests()
        
        # 生成最终评估
        final_results = validator.generate_final_assessment()
        
        # 保存详细结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"institutional_timestamp_fix_validation_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细测试结果已保存: {result_file}")
        
        # 根据结果决定退出码
        if final_results["deployment_ready"]:
            print("\n✅ 修复验证通过！可以部署到生产环境。")
            return 0
        else:
            print("\n❌ 修复验证失败！需要继续改进才能部署。")
            return 1
            
    except Exception as e:
        print(f"\n💥 验证过程发生异常: {e}")
        print(f"堆栈跟踪: {traceback.format_exc()}")
        return 2

if __name__ == "__main__":
    exit_code = asyncio.run(main())