#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳同步修复验证脚本
验证ArbitrageEngine中新增的时间戳同步系统是否正常工作

验证内容：
1. 系统启动时是否自动同步所有交易所时间戳处理器
2. 跨交易所时间戳一致性是否满足1000ms阈值要求
3. 时间戳监控和自动重同步是否正常工作
4. 修复后是否还会出现"价格数据时间戳不同步，丢弃套利机会"错误
"""

import asyncio
import sys
import os
import time
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def main():
    """主验证流程"""
    print("🔥 时间戳同步修复验证")
    print("=" * 60)
    
    # 第1步：验证修复后的ArbitrageEngine时间戳同步
    print("\n📋 第1步：验证ArbitrageEngine时间戳同步系统")
    engine_sync_test = await test_arbitrage_engine_timestamp_sync()
    
    # 第2步：模拟跨交易所时间戳验证（修复后）
    print("\n📋 第2步：模拟修复后的跨交易所时间戳验证")
    cross_exchange_test = await test_fixed_cross_exchange_sync()
    
    # 第3步：压力测试时间戳同步稳定性
    print("\n📋 第3步：压力测试时间戳同步稳定性")
    stability_test = await test_timestamp_sync_stability()
    
    # 第4步：生成修复验证报告
    print("\n📋 第4步：生成修复验证报告")
    verification_report = generate_fix_verification_report(
        engine_sync_test,
        cross_exchange_test,
        stability_test
    )
    
    # 保存验证结果
    save_verification_results(verification_report)
    
    print("\n✅ 修复验证完成！请查看验证报告了解修复效果。")

async def test_arbitrage_engine_timestamp_sync():
    """测试ArbitrageEngine的时间戳同步系统"""
    print("🔍 测试ArbitrageEngine时间戳同步系统...")
    
    results = {
        "engine_import_success": False,
        "sync_methods_available": False,
        "sync_execution_test": {},
        "monitoring_system_test": {},
        "error_details": []
    }
    
    try:
        # 导入ArbitrageEngine
        from core.arbitrage_engine import ArbitrageEngine
        results["engine_import_success"] = True
        print("✅ ArbitrageEngine导入成功")
        
        # 创建ArbitrageEngine实例
        engine = ArbitrageEngine()
        
        # 检查新增的时间戳同步方法是否存在
        sync_methods = [
            '_initialize_timestamp_sync',
            '_sync_single_exchange_timestamp', 
            '_validate_cross_exchange_timestamp_consistency',
            '_setup_timestamp_monitoring',
            '_check_timestamp_sync_health',
            '_resync_failed_exchanges'
        ]
        
        missing_methods = []
        for method in sync_methods:
            if not hasattr(engine, method):
                missing_methods.append(method)
        
        if not missing_methods:
            results["sync_methods_available"] = True
            print("✅ 所有时间戳同步方法都已正确添加")
        else:
            results["error_details"].append(f"缺少方法: {missing_methods}")
            print(f"❌ 缺少时间戳同步方法: {missing_methods}")
        
        # 测试时间戳同步执行
        if results["sync_methods_available"]:
            print("🧪 测试时间戳同步执行...")
            
            sync_start = time.time()
            sync_results = await engine._initialize_timestamp_sync()
            sync_duration = time.time() - sync_start
            
            results["sync_execution_test"] = {
                "execution_successful": isinstance(sync_results, dict),
                "sync_duration": sync_duration,
                "sync_results_count": len(sync_results) if isinstance(sync_results, dict) else 0,
                "sync_results": sync_results
            }
            
            if results["sync_execution_test"]["execution_successful"]:
                success_count = sum(1 for r in sync_results.values() if r.get("success", False))
                total_count = len(sync_results)
                print(f"✅ 时间戳同步执行成功: {success_count}/{total_count} 交易所同步成功")
                print(f"   耗时: {sync_duration:.2f}秒")
            else:
                print("❌ 时间戳同步执行失败")
                results["error_details"].append("时间戳同步执行失败")
        
        # 测试监控系统
        if results["sync_methods_available"]:
            print("🧪 测试时间戳监控系统...")
            
            try:
                # 测试健康检查方法
                await engine._check_timestamp_sync_health()
                results["monitoring_system_test"]["health_check"] = True
                print("✅ 时间戳健康检查正常")
            except Exception as e:
                results["monitoring_system_test"]["health_check"] = False
                results["error_details"].append(f"健康检查失败: {str(e)}")
                print(f"❌ 时间戳健康检查失败: {e}")
            
            try:
                # 测试一致性验证方法
                await engine._validate_cross_exchange_timestamp_consistency()
                results["monitoring_system_test"]["consistency_validation"] = True
                print("✅ 跨交易所时间戳一致性验证正常")
            except Exception as e:
                results["monitoring_system_test"]["consistency_validation"] = False
                results["error_details"].append(f"一致性验证失败: {str(e)}")
                print(f"❌ 跨交易所时间戳一致性验证失败: {e}")
        
    except ImportError as e:
        print(f"❌ ArbitrageEngine导入失败: {e}")
        results["error_details"].append(f"导入失败: {str(e)}")
    except Exception as e:
        print(f"❌ ArbitrageEngine测试异常: {e}")
        results["error_details"].append(f"测试异常: {str(e)}")
    
    return results

async def test_fixed_cross_exchange_sync():
    """测试修复后的跨交易所时间戳同步"""
    print("🔍 测试修复后的跨交易所时间戳同步...")
    
    results = {
        "processor_sync_test": {},
        "cross_exchange_consistency": {},
        "threshold_compliance": {},
        "error_details": []
    }
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        exchanges = ["gate", "bybit", "okx"]
        
        # 首先强制同步所有交易所（模拟ArbitrageEngine的行为）
        print("🔄 强制同步所有交易所时间戳处理器...")
        sync_results = {}
        
        for exchange in exchanges:
            try:
                processor = get_timestamp_processor(exchange)
                sync_success = await processor.sync_time(force=True)
                status = processor.get_sync_status()
                
                sync_results[exchange] = {
                    "sync_success": sync_success,
                    "time_synced": status.get("time_synced", False),
                    "offset_ms": status.get("time_offset_ms", 0)
                }
                
                if sync_success:
                    print(f"✅ {exchange.upper()} 同步成功，偏移: {status.get('time_offset_ms', 0)}ms")
                else:
                    print(f"❌ {exchange.upper()} 同步失败")
                    
            except Exception as e:
                print(f"❌ {exchange.upper()} 同步异常: {e}")
                sync_results[exchange] = {"error": str(e)}
        
        results["processor_sync_test"] = sync_results
        
        # 测试跨交易所时间戳一致性
        print("🧪 测试跨交易所时间戳一致性...")
        
        timestamps = {}
        for exchange in exchanges:
            try:
                processor = get_timestamp_processor(exchange)
                timestamp = processor.get_synced_timestamp()
                timestamps[exchange] = timestamp
            except Exception as e:
                results["error_details"].append(f"{exchange}时间戳获取失败: {str(e)}")
                continue
        
        if len(timestamps) >= 2:
            timestamp_values = list(timestamps.values())
            max_diff = max(timestamp_values) - min(timestamp_values)
            avg_timestamp = sum(timestamp_values) / len(timestamp_values)
            
            results["cross_exchange_consistency"] = {
                "timestamps": timestamps,
                "max_difference_ms": max_diff,
                "average_timestamp": avg_timestamp,
                "within_threshold": max_diff <= 1000
            }
            
            print(f"📊 跨交易所时间戳差异: {max_diff:.1f}ms")
            
            if max_diff <= 1000:
                print("✅ 时间戳差异在1000ms阈值内")
            else:
                print(f"⚠️ 时间戳差异超过阈值: {max_diff:.1f}ms > 1000ms")
                
                # 详细分析差异
                exchange_list = list(timestamps.keys())
                for i in range(len(exchange_list)):
                    for j in range(i+1, len(exchange_list)):
                        diff = abs(timestamps[exchange_list[i]] - timestamps[exchange_list[j]])
                        print(f"   {exchange_list[i]} vs {exchange_list[j]}: {diff:.1f}ms")
        
        # 测试阈值合规性（模拟OpportunityScanner的验证逻辑）
        print("🧪 测试阈值合规性...")
        
        threshold_test_scenarios = [
            {"name": "正常范围", "time_diff": 500},
            {"name": "边界值", "time_diff": 1000},
            {"name": "轻微超标", "time_diff": 1100}
        ]
        
        threshold_results = {}
        
        for scenario in threshold_test_scenarios:
            current_time = int(time.time() * 1000)
            spot_timestamp = current_time
            futures_timestamp = current_time - scenario["time_diff"]
            
            # 使用第一个可用的处理器进行验证
            if timestamps:
                first_exchange = list(timestamps.keys())[0]
                processor = get_timestamp_processor(first_exchange)
                
                is_synced, actual_diff = processor.validate_cross_exchange_sync(
                    spot_timestamp, futures_timestamp,
                    first_exchange, "futures_exchange",
                    max_diff_ms=1000
                )
                
                threshold_results[scenario["name"]] = {
                    "expected_diff": scenario["time_diff"],
                    "actual_diff": actual_diff,
                    "is_synced": is_synced,
                    "expected_result": scenario["time_diff"] <= 1000
                }
                
                expected = scenario["time_diff"] <= 1000
                if is_synced == expected:
                    print(f"✅ {scenario['name']} ({scenario['time_diff']}ms): 验证正确")
                else:
                    print(f"❌ {scenario['name']} ({scenario['time_diff']}ms): 验证错误，预期{expected}，实际{is_synced}")
        
        results["threshold_compliance"] = threshold_results
        
    except Exception as e:
        print(f"❌ 跨交易所时间戳同步测试失败: {e}")
        results["error_details"].append(f"测试失败: {str(e)}")
    
    return results

async def test_timestamp_sync_stability():
    """压力测试时间戳同步稳定性"""
    print("🔍 压力测试时间戳同步稳定性...")
    
    results = {
        "stability_test_successful": False,
        "repeated_sync_results": [],
        "timestamp_variance_analysis": {},
        "performance_metrics": {},
        "error_details": []
    }
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        exchanges = ["gate", "bybit", "okx"]
        test_rounds = 5  # 进行5轮测试
        
        print(f"🧪 进行{test_rounds}轮时间戳同步稳定性测试...")
        
        all_round_results = []
        
        for round_num in range(test_rounds):
            print(f"\n🔄 第{round_num + 1}轮测试:")
            
            round_start = time.time()
            round_results = {}
            
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    
                    # 强制重新同步
                    sync_success = await processor.sync_time(force=True)
                    status = processor.get_sync_status()
                    
                    # 获取时间戳
                    timestamp = processor.get_synced_timestamp()
                    current_time = int(time.time() * 1000)
                    time_diff = abs(timestamp - current_time)
                    
                    round_results[exchange] = {
                        "sync_success": sync_success,
                        "time_synced": status.get("time_synced", False),
                        "offset_ms": status.get("time_offset_ms", 0),
                        "timestamp": timestamp,
                        "time_diff_from_system": time_diff
                    }
                    
                    if sync_success:
                        print(f"  ✅ {exchange}: 同步成功，偏移{status.get('time_offset_ms', 0)}ms，时差{time_diff:.1f}ms")
                    else:
                        print(f"  ❌ {exchange}: 同步失败")
                        
                except Exception as e:
                    print(f"  ❌ {exchange}: 测试异常 - {e}")
                    round_results[exchange] = {"error": str(e)}
            
            round_duration = time.time() - round_start
            round_results["round_duration"] = round_duration
            round_results["round_number"] = round_num + 1
            
            all_round_results.append(round_results)
            
            # 轮次间隔
            if round_num < test_rounds - 1:
                await asyncio.sleep(2)  # 2秒间隔
        
        results["repeated_sync_results"] = all_round_results
        
        # 分析稳定性
        print("\n📊 分析时间戳同步稳定性...")
        
        exchange_stability = {}
        for exchange in exchanges:
            offsets = []
            success_count = 0
            
            for round_result in all_round_results:
                if exchange in round_result and "offset_ms" in round_result[exchange]:
                    offsets.append(round_result[exchange]["offset_ms"])
                    if round_result[exchange].get("sync_success", False):
                        success_count += 1
            
            if offsets:
                avg_offset = sum(offsets) / len(offsets)
                max_offset = max(offsets)
                min_offset = min(offsets)
                offset_variance = max_offset - min_offset
                
                exchange_stability[exchange] = {
                    "success_rate": success_count / test_rounds,
                    "average_offset": avg_offset,
                    "max_offset": max_offset,
                    "min_offset": min_offset,
                    "offset_variance": offset_variance,
                    "stable": offset_variance <= 100  # 偏移变化小于100ms认为稳定
                }
                
                print(f"  {exchange}: 成功率{success_rate*100:.1f}%, 平均偏移{avg_offset:.1f}ms, 变化幅度{offset_variance:.1f}ms")
        
        results["timestamp_variance_analysis"] = exchange_stability
        
        # 性能指标
        durations = [r.get("round_duration", 0) for r in all_round_results]
        results["performance_metrics"] = {
            "average_sync_duration": sum(durations) / len(durations),
            "max_sync_duration": max(durations),
            "min_sync_duration": min(durations),
            "total_test_duration": sum(durations)
        }
        
        # 总体稳定性评估
        stable_exchanges = sum(1 for s in exchange_stability.values() if s.get("stable", False))
        high_success_rate_exchanges = sum(1 for s in exchange_stability.values() if s.get("success_rate", 0) >= 0.8)
        
        results["stability_test_successful"] = (
            stable_exchanges >= 2 and 
            high_success_rate_exchanges >= 2 and
            results["performance_metrics"]["average_sync_duration"] < 5.0
        )
        
        if results["stability_test_successful"]:
            print("✅ 时间戳同步稳定性测试通过")
        else:
            print("⚠️ 时间戳同步稳定性需要改进")
            
    except Exception as e:
        print(f"❌ 时间戳同步稳定性测试失败: {e}")
        results["error_details"].append(f"稳定性测试失败: {str(e)}")
    
    return results

def generate_fix_verification_report(engine_test, cross_exchange_test, stability_test):
    """生成修复验证报告"""
    print("📋 生成修复验证报告...")
    
    report = {
        "verification_timestamp": datetime.now().isoformat(),
        "fix_verification_summary": {
            "overall_status": "UNKNOWN",
            "fix_effectiveness": "UNKNOWN", 
            "critical_issues_resolved": [],
            "remaining_issues": [],
            "recommendations": []
        },
        "detailed_test_results": {
            "engine_test": engine_test,
            "cross_exchange_test": cross_exchange_test,
            "stability_test": stability_test
        },
        "fix_impact_analysis": {},
        "deployment_readiness": {}
    }
    
    # 分析修复效果
    critical_resolved = []
    remaining_issues = []
    recommendations = []
    
    # 分析ArbitrageEngine修复
    if engine_test.get("engine_import_success", False) and engine_test.get("sync_methods_available", False):
        critical_resolved.append("系统启动时自动时间戳同步功能已添加")
        
        if engine_test.get("sync_execution_test", {}).get("execution_successful", False):
            critical_resolved.append("时间戳同步执行正常")
        else:
            remaining_issues.append("时间戳同步执行仍有问题")
            
        if engine_test.get("monitoring_system_test", {}).get("health_check", False):
            critical_resolved.append("时间戳健康监控系统正常")
        else:
            remaining_issues.append("时间戳监控系统需要完善")
    else:
        remaining_issues.append("ArbitrageEngine时间戳同步系统不完整")
    
    # 分析跨交易所一致性
    consistency_result = cross_exchange_test.get("cross_exchange_consistency", {})
    if consistency_result.get("within_threshold", False):
        critical_resolved.append("跨交易所时间戳一致性满足1000ms阈值要求")
    else:
        max_diff = consistency_result.get("max_difference_ms", float('inf'))
        remaining_issues.append(f"跨交易所时间戳差异仍超过阈值({max_diff:.1f}ms)")
        recommendations.append("需要进一步优化时间同步算法")
    
    # 分析稳定性
    if stability_test.get("stability_test_successful", False):
        critical_resolved.append("时间戳同步稳定性测试通过")
    else:
        remaining_issues.append("时间戳同步稳定性需要改进")
        recommendations.append("建议增加重试机制和错误恢复策略")
    
    # 综合评估
    fix_score = len(critical_resolved) / (len(critical_resolved) + len(remaining_issues)) if (critical_resolved or remaining_issues) else 0
    
    if fix_score >= 0.8:
        report["fix_verification_summary"]["overall_status"] = "FIXED"
        report["fix_verification_summary"]["fix_effectiveness"] = "HIGH"
    elif fix_score >= 0.6:
        report["fix_verification_summary"]["overall_status"] = "PARTIALLY_FIXED" 
        report["fix_verification_summary"]["fix_effectiveness"] = "MEDIUM"
    else:
        report["fix_verification_summary"]["overall_status"] = "NEEDS_MORE_WORK"
        report["fix_verification_summary"]["fix_effectiveness"] = "LOW"
    
    # 部署就绪性评估
    deployment_ready = (
        fix_score >= 0.7 and
        consistency_result.get("within_threshold", False) and
        len(remaining_issues) <= 2
    )
    
    report["fix_verification_summary"]["critical_issues_resolved"] = critical_resolved
    report["fix_verification_summary"]["remaining_issues"] = remaining_issues
    report["fix_verification_summary"]["recommendations"] = recommendations
    
    report["deployment_readiness"] = {
        "ready_for_deployment": deployment_ready,
        "fix_score": fix_score,
        "confidence_level": "HIGH" if fix_score >= 0.8 else "MEDIUM" if fix_score >= 0.6 else "LOW"
    }
    
    # 打印报告摘要
    print(f"\n📊 修复验证结果摘要:")
    print(f"  整体状态: {report['fix_verification_summary']['overall_status']}")
    print(f"  修复效果: {report['fix_verification_summary']['fix_effectiveness']}")
    print(f"  修复评分: {fix_score*100:.1f}%")
    print(f"  部署就绪: {'是' if deployment_ready else '否'}")
    
    if critical_resolved:
        print(f"  已解决关键问题:")
        for issue in critical_resolved:
            print(f"    ✅ {issue}")
    
    if remaining_issues:
        print(f"  仍存在问题:")
        for issue in remaining_issues:
            print(f"    ⚠️ {issue}")
    
    return report

def save_verification_results(report):
    """保存验证结果到文件"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"timestamp_sync_fix_verification_{timestamp}.json"
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 修复验证报告已保存到: {filepath}")
        
        # 保存简化版文本报告
        text_filename = f"timestamp_sync_fix_verification_{timestamp}.txt"
        text_filepath = os.path.join(os.path.dirname(__file__), text_filename)
        
        with open(text_filepath, 'w', encoding='utf-8') as f:
            f.write("时间戳同步修复验证报告\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"验证时间: {report['verification_timestamp']}\n")
            f.write(f"整体状态: {report['fix_verification_summary']['overall_status']}\n")
            f.write(f"修复效果: {report['fix_verification_summary']['fix_effectiveness']}\n")
            f.write(f"部署就绪: {'是' if report['deployment_readiness']['ready_for_deployment'] else '否'}\n\n")
            
            f.write("已解决的关键问题:\n")
            for issue in report['fix_verification_summary']['critical_issues_resolved']:
                f.write(f"  ✅ {issue}\n")
            f.write("\n")
            
            f.write("仍存在的问题:\n")
            for issue in report['fix_verification_summary']['remaining_issues']:
                f.write(f"  ⚠️ {issue}\n")
            f.write("\n")
            
            f.write("修复建议:\n")
            for rec in report['fix_verification_summary']['recommendations']:
                f.write(f"  💡 {rec}\n")
        
        print(f"💾 简化文本报告已保存到: {text_filepath}")
        
    except Exception as e:
        print(f"❌ 保存验证结果失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())