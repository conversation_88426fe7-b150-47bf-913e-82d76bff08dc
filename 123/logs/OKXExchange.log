2025-08-02 16:48:02 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-02 16:48:02 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-02 16:48:02 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-02 16:48:02 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-02 16:48:02.245 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-02 16:48:02.245 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-02 16:48:02 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-02 16:48:02 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-02 16:48:02 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-02 16:48:02 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-02 16:48:02.738 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:48:03.051 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:03.051 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:03.391 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:48:03.391 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:48:03 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-02 16:48:03.391 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:48:03.701 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:03.701 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:04.054 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:48:04.054 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:48:04 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-02 16:48:04.054 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:48:04.354 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:04.354 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:04.690 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:48:04.690 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:48:04 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-02 16:48:04.690 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:48:05.015 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:05.017 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:05.363 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:48:05.363 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:48:05 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-02 16:48:05 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-02 16:48:05.709 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 16:48:07.048 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 16:48:08.555 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:48:09.144 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:48:09.733 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:48:10.055 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 16:48:10.713 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 16:48:11.299 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 16:48:31.152 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:31.152 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:31.809 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:31.809 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:32.587 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:32.587 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:33.132 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:33.132 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:33.790 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:33.790 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:48:34.458 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:48:34.458 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:16.655 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 16:49:16.987 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 16:49:17.003 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:49:17.010 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 16:49:17.011 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-02 16:49:19.422 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-02 16:49:19.756 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-02 16:49:19.756 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-02 16:49:19.763 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-02 16:49:19.764 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-02 16:49:20.765 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.097 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.098 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.098 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.098 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.099 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.099 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.099 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.099 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.099 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 16:49:21.179 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:21.179 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:21.501 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:21.501 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:21.510 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:21.511 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:21.513 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:21.514 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:21.515 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:21.515 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.518 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.518 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.518 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.519 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 16:49:21.525 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:21.525 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:21.526 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 16:49:21.541 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.541 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 16:49:21.838 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:21.839 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:21.844 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:21.844 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:21.885 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:21.886 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:21.887 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:21.887 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:23.602 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:23.602 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:23.920 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:23.920 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:23.923 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:23.924 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:23.928 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:23.928 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:23.932 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-02 16:49:23.932 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 16:49:23.933 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:23.933 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:24.258 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:24.259 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:24.259 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:24.259 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:24.276 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:24.277 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:24.278 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 16:49:24.278 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 16:49:35.249 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 16:49:36.736 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 16:49:38.270 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:49:38.924 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:49:39.496 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:49:39.828 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 16:49:40.402 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 16:49:40.972 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 16:49:45.768 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 16:49:47.258 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 16:49:48.801 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:49:49.424 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:49:49.997 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 16:49:50.326 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
