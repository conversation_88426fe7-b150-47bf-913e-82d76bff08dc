2025-08-02 17:19:58 [HEARTBEAT] INFO - 心跳监控日志记录器初始化完成
2025-08-02 17:19:58 [HEARTBEAT] INFO - [OKX] 心跳发送 | {"event": "heartbeat_send", "exchange": "okx", "ping_time": 1754147998.5707536, "success": true, "timestamp": 1754147998.5712764, "test_mode": true}
2025-08-02 17:19:58 [HEARTBEAT] INFO - [GATE] 心跳响应 | {"event": "heartbeat_response", "exchange": "gate", "pong_time": 1754147998.5707536, "response_type": "pong", "timestamp": 1754147998.5714326, "test_mode": true}
2025-08-02 17:19:58 [HEARTBEAT] ERROR - [BYBIT] 心跳失败 | {"event": "heartbeat_failure", "exchange": "bybit", "error_message": "测试错误", "timestamp": 1754147998.5715246, "test_mode": true}
2025-08-02 17:19:58 [HEARTBEAT] ERROR - [OKX] HTTP 503错误 | {"event": "http_503_error", "exchange": "okx", "url": "wss://ws.okx.com:8443/ws/v5/public", "retry_count": 3, "timestamp": 1754147998.5716631, "test_mode": true}
2025-08-02 17:19:58 [HEARTBEAT] WARNING - [OKX] 端点切换 | {"event": "endpoint_switch", "exchange": "okx", "old_url": "old_url", "new_url": "new_url", "reason": "http_503_retry_limit", "timestamp": 1754147998.5717561, "test_mode": true}
2025-08-02 17:20:01 [HEARTBEAT] INFO - 心跳监控日志记录器初始化完成
2025-08-02 17:20:01 [HEARTBEAT] INFO - [OKX] 心跳发送 | {"event": "heartbeat_send", "exchange": "okx", "ping_time": 1754148001.4572475, "success": true, "timestamp": 1754148001.4576547, "test_mode": true}
2025-08-02 17:20:01 [HEARTBEAT] INFO - [GATE] 心跳响应 | {"event": "heartbeat_response", "exchange": "gate", "pong_time": 1754148001.4572475, "response_type": "pong", "timestamp": 1754148001.4577782, "test_mode": true}
2025-08-02 17:20:01 [HEARTBEAT] ERROR - [BYBIT] 心跳失败 | {"event": "heartbeat_failure", "exchange": "bybit", "error_message": "测试错误", "timestamp": 1754148001.4578655, "test_mode": true}
2025-08-02 17:20:01 [HEARTBEAT] ERROR - [OKX] HTTP 503错误 | {"event": "http_503_error", "exchange": "okx", "url": "wss://ws.okx.com:8443/ws/v5/public", "retry_count": 3, "timestamp": 1754148001.457962, "test_mode": true}
2025-08-02 17:20:01 [HEARTBEAT] WARNING - [OKX] 端点切换 | {"event": "endpoint_switch", "exchange": "okx", "old_url": "old_url", "new_url": "new_url", "reason": "http_503_retry_limit", "timestamp": 1754148001.4580734, "test_mode": true}
2025-08-02 17:29:17 [HEARTBEAT] INFO - 心跳监控日志记录器初始化完成
2025-08-02 17:29:17 [HEARTBEAT] INFO - [TEST] 心跳发送 | {"event": "heartbeat_send", "exchange": "test", "ping_time": 1754148557.9621155, "success": true, "timestamp": 1754148557.9623384, "test_param": "test_value"}
2025-08-02 17:29:17 [HEARTBEAT] INFO - [TEST] 心跳响应 | {"event": "heartbeat_response", "exchange": "test", "pong_time": 1754148557.9621155, "response_type": "test_response", "timestamp": 1754148557.9624527}
2025-08-02 17:29:17 [HEARTBEAT] ERROR - [TEST] 心跳失败 | {"event": "heartbeat_failure", "exchange": "test", "error_message": "test_error", "timestamp": 1754148557.9625278}
2025-08-02 17:33:15 [HEARTBEAT] INFO - 心跳监控日志记录器初始化完成
2025-08-02 17:33:15 [HEARTBEAT] INFO - [TEST] 心跳发送 | {"event": "heartbeat_send", "exchange": "test", "ping_time": 1754148795.7823615, "success": true, "timestamp": 1754148795.7826784, "test_param": "test_value"}
2025-08-02 17:33:15 [HEARTBEAT] INFO - [TEST] 心跳响应 | {"event": "heartbeat_response", "exchange": "test", "pong_time": 1754148795.7823615, "response_type": "test_response", "timestamp": 1754148795.7828162}
2025-08-02 17:33:15 [HEARTBEAT] ERROR - [TEST] 心跳失败 | {"event": "heartbeat_failure", "exchange": "test", "error_message": "test_error", "timestamp": 1754148795.7828994}
2025-08-02 17:33:15 [HEARTBEAT] ERROR - [TEST] HTTP 503错误 | {"event": "http_503_error", "exchange": "test", "url": "test_503_error", "retry_count": 1, "timestamp": 1754148795.7829921}
