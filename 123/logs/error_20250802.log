2025-08-02 16:48:09.733 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-02 16:48:11.299 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.517 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.518 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.518 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.518 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.519 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21.526 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.527 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21.541 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.541 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.542 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.543 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.544 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 16:49:21.570 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 16:49:21.571 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 16:49:22.687 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-02 16:49:22.687 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-02 16:49:22.687 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-02 16:49:32.970 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/ws_client.py", line 265, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-02 16:49:32.977 [ERROR] [UnifiedConnectionPoolManager] ❌ 连接OKX_websocket不存在，无法重连
2025-08-02 16:49:32.977 [ERROR] [websocket] [OKX] 统一连接池管理器重连失败
2025-08-02 16:49:32.977 [ERROR] [websocket] [OKX] 无法建立WebSocket连接，退出
2025-08-02 16:49:32.978 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
2025-08-02 16:49:33.979 [ERROR] [websocket.error_handler] 恢复尝试异常: WebSocketClient._reconnect() takes 1 positional argument but 2 were given
2025-08-02 16:49:35.980 [ERROR] [websocket.error_handler] 恢复尝试异常: WebSocketClient._reconnect() takes 1 positional argument but 2 were given
2025-08-02 16:49:39.497 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-02 16:49:39.979 [ERROR] [websocket.error_handler] 恢复尝试异常: WebSocketClient._reconnect() takes 1 positional argument but 2 were given
2025-08-02 16:49:40.972 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-02 16:49:47.980 [ERROR] [websocket.error_handler] 恢复尝试异常: WebSocketClient._reconnect() takes 1 positional argument but 2 were given
2025-08-02 16:49:49.997 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-02 16:49:51.529 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-02 16:50:04.079 [ERROR] [websocket.error_handler] 恢复尝试异常: WebSocketClient._reconnect() takes 1 positional argument but 2 were given
2025-08-02 16:50:04.079 [ERROR] [websocket.error_handler] [OKX] 恢复失败，已达到最大重试次数
2025-08-02 16:50:04.079 [ERROR] [asyncio] Task exception was never retrieved
future: <Task finished name='Task-326' coro=<UnifiedErrorHandler.handle_error() done, defined at /root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/error_handler.py:183> exception=TypeError("'int' object is not iterable")>
Traceback (most recent call last):
  File "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/error_handler.py", line 206, in handle_error
    return await self._execute_recovery_strategy(error_event, strategy, context or {})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/error_handler.py", line 234, in _execute_recovery_strategy
    self._update_recovery_stats(False)
  File "/root/myproject/123/67E okx 解决了 gate还有问题/123/websocket/error_handler.py", line 288, in _update_recovery_stats
    total_attempts = sum(len([e for e in self.error_events if e.retry_count > 0]))
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'int' object is not iterable
