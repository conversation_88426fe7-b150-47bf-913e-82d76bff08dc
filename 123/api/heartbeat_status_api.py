"""
🔥 心跳监控状态查询API
提供统一的心跳监控状态查询接口

功能：
1. 查询所有交易所心跳状态
2. 查询单个交易所心跳详情
3. 获取心跳失败分析报告
4. 提供心跳健康度评估
5. 心跳性能趋势分析
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from websocket.heartbeat_monitor import get_heartbeat_monitor, HeartbeatStatus


class HeartbeatStatusResponse(BaseModel):
    """心跳状态响应模型"""
    exchange: str
    status: str
    health_score: float  # 健康度评分 0-100
    metrics: Dict[str, Any]
    recommendations: List[str]  # 改进建议


class HeartbeatSummaryResponse(BaseModel):
    """心跳状态汇总响应模型"""
    summary: Dict[str, Any]
    exchanges: Dict[str, HeartbeatStatusResponse]
    overall_health: float  # 整体健康度
    alerts: List[str]  # 告警信息


router = APIRouter(prefix="/api/heartbeat", tags=["心跳监控"])


@router.get("/status", response_model=HeartbeatSummaryResponse)
async def get_all_heartbeat_status():
    """🔥 获取所有交易所心跳状态"""
    try:
        heartbeat_monitor = get_heartbeat_monitor()
        status_data = heartbeat_monitor.get_heartbeat_status()
        
        # 计算健康度和生成建议
        exchanges_response = {}
        total_health_score = 0
        alerts = []
        
        for exchange, exchange_data in status_data.get("exchanges", {}).items():
            health_score, recommendations = _calculate_health_score(exchange_data)
            
            exchanges_response[exchange] = HeartbeatStatusResponse(
                exchange=exchange,
                status=exchange_data["status"],
                health_score=health_score,
                metrics=exchange_data["metrics"],
                recommendations=recommendations
            )
            
            total_health_score += health_score
            
            # 生成告警
            if health_score < 50:
                alerts.append(f"🚨 {exchange} 心跳健康度严重不足 ({health_score:.1f}%)")
            elif health_score < 80:
                alerts.append(f"⚠️ {exchange} 心跳健康度需要关注 ({health_score:.1f}%)")
        
        # 计算整体健康度
        overall_health = total_health_score / len(exchanges_response) if exchanges_response else 0
        
        return HeartbeatSummaryResponse(
            summary=status_data["summary"],
            exchanges=exchanges_response,
            overall_health=overall_health,
            alerts=alerts
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取心跳状态失败: {str(e)}")


@router.get("/status/{exchange}", response_model=HeartbeatStatusResponse)
async def get_exchange_heartbeat_status(exchange: str):
    """🔥 获取单个交易所心跳状态"""
    try:
        heartbeat_monitor = get_heartbeat_monitor()
        status_data = heartbeat_monitor.get_heartbeat_status(exchange)
        
        if status_data.get("status") == "not_monitored":
            raise HTTPException(status_code=404, detail=f"交易所 {exchange} 未被监控")
        
        health_score, recommendations = _calculate_health_score(status_data)
        
        return HeartbeatStatusResponse(
            exchange=exchange,
            status=status_data["status"],
            health_score=health_score,
            metrics=status_data["metrics"],
            recommendations=recommendations
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取心跳状态失败: {str(e)}")


@router.get("/analysis/{exchange}")
async def get_heartbeat_analysis(exchange: str):
    """🔥 获取心跳失败分析报告"""
    try:
        heartbeat_monitor = get_heartbeat_monitor()
        status_data = heartbeat_monitor.get_heartbeat_status(exchange)
        
        if status_data.get("status") == "not_monitored":
            raise HTTPException(status_code=404, detail=f"交易所 {exchange} 未被监控")
        
        metrics = status_data["metrics"]
        
        # 分析失败原因
        failure_analysis = _analyze_failures(metrics)
        
        # 性能分析
        performance_analysis = _analyze_performance(metrics)
        
        # 趋势分析
        trend_analysis = _analyze_trends(metrics)
        
        return {
            "exchange": exchange,
            "analysis_time": int(time.time() * 1000),
            "failure_analysis": failure_analysis,
            "performance_analysis": performance_analysis,
            "trend_analysis": trend_analysis,
            "recommendations": _generate_detailed_recommendations(metrics, failure_analysis, performance_analysis)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取心跳分析失败: {str(e)}")


@router.post("/reset/{exchange}")
async def reset_heartbeat_metrics(exchange: str):
    """🔥 重置心跳监控指标"""
    try:
        heartbeat_monitor = get_heartbeat_monitor()
        
        # 检查交易所是否存在
        status_data = heartbeat_monitor.get_heartbeat_status(exchange)
        if status_data.get("status") == "not_monitored":
            raise HTTPException(status_code=404, detail=f"交易所 {exchange} 未被监控")
        
        # 重置指标（通过重新初始化）
        if exchange in heartbeat_monitor.heartbeat_metrics:
            from websocket.heartbeat_monitor import HeartbeatMetrics, HeartbeatStatus
            heartbeat_monitor.heartbeat_metrics[exchange] = HeartbeatMetrics()
            heartbeat_monitor.current_status[exchange] = HeartbeatStatus.UNKNOWN
        
        return {"message": f"交易所 {exchange} 心跳监控指标已重置"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置心跳指标失败: {str(e)}")


def _calculate_health_score(status_data: Dict[str, Any]) -> tuple[float, List[str]]:
    """🔥 计算健康度评分"""
    metrics = status_data.get("metrics", {})
    status = status_data.get("status", "unknown")
    
    # 基础分数
    base_score = 100.0
    recommendations = []
    
    # 根据状态扣分
    status_penalties = {
        "healthy": 0,
        "warning": 10,
        "critical": 30,
        "failed": 60,
        "unknown": 50
    }
    base_score -= status_penalties.get(status, 50)
    
    # 根据成功率扣分
    success_rate = metrics.get("success_rate", 0)
    if success_rate < 95:
        penalty = (95 - success_rate) * 0.5
        base_score -= penalty
        recommendations.append(f"成功率偏低 ({success_rate:.1f}%)，建议检查网络连接")
    
    # 根据连续失败次数扣分
    consecutive_failures = metrics.get("consecutive_failures", 0)
    if consecutive_failures > 0:
        penalty = min(consecutive_failures * 5, 20)
        base_score -= penalty
        recommendations.append(f"存在连续失败 ({consecutive_failures}次)，建议检查连接稳定性")
    
    # 根据响应时间扣分
    avg_response_time = metrics.get("avg_response_time", 0)
    if avg_response_time > 1000:  # 超过1秒
        penalty = min((avg_response_time - 1000) / 100, 15)
        base_score -= penalty
        recommendations.append(f"响应时间过长 ({avg_response_time:.1f}ms)，建议优化网络")
    
    # 根据失败原因扣分
    failure_reasons = metrics.get("failure_reasons", {})
    if failure_reasons:
        total_failures = sum(failure_reasons.values())
        if total_failures > 10:
            penalty = min(total_failures * 0.1, 10)
            base_score -= penalty
            recommendations.append("失败次数较多，建议分析失败原因")
    
    # 确保分数在0-100范围内
    health_score = max(0, min(100, base_score))
    
    # 添加通用建议
    if health_score < 80:
        recommendations.append("建议启用多节点智能切换功能")
        recommendations.append("建议检查网络配置和防火墙设置")
    
    return health_score, recommendations


def _analyze_failures(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """🔥 分析失败原因"""
    failure_reasons = metrics.get("failure_reasons", {})
    total_failures = metrics.get("total_failed", 0)
    
    if total_failures == 0:
        return {"status": "no_failures", "message": "暂无失败记录"}
    
    # 分析主要失败原因
    main_reason = max(failure_reasons.items(), key=lambda x: x[1]) if failure_reasons else ("unknown", 0)
    
    return {
        "total_failures": total_failures,
        "main_failure_reason": main_reason[0],
        "main_failure_count": main_reason[1],
        "failure_distribution": failure_reasons,
        "failure_rate": (total_failures / metrics.get("total_sent", 1)) * 100
    }


def _analyze_performance(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """🔥 分析性能指标"""
    return {
        "avg_response_time": metrics.get("avg_response_time", 0),
        "max_response_time": metrics.get("max_response_time", 0),
        "min_response_time": metrics.get("min_response_time", 0),
        "performance_grade": _get_performance_grade(metrics.get("avg_response_time", 0))
    }


def _analyze_trends(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """🔥 分析趋势"""
    last_success_time = metrics.get("last_success_time", 0)
    last_failure_time = metrics.get("last_failure_time", 0)
    current_time = time.time()
    
    return {
        "time_since_last_success": current_time - last_success_time if last_success_time > 0 else -1,
        "time_since_last_failure": current_time - last_failure_time if last_failure_time > 0 else -1,
        "recent_status": "improving" if last_success_time > last_failure_time else "degrading"
    }


def _get_performance_grade(avg_response_time: float) -> str:
    """🔥 获取性能等级"""
    if avg_response_time < 100:
        return "优秀"
    elif avg_response_time < 300:
        return "良好"
    elif avg_response_time < 1000:
        return "一般"
    else:
        return "较差"


def _generate_detailed_recommendations(metrics: Dict[str, Any], 
                                     failure_analysis: Dict[str, Any],
                                     performance_analysis: Dict[str, Any]) -> List[str]:
    """🔥 生成详细建议"""
    recommendations = []
    
    # 基于失败分析的建议
    if failure_analysis.get("total_failures", 0) > 0:
        main_reason = failure_analysis.get("main_failure_reason", "")
        if main_reason == "connection_closed":
            recommendations.append("主要失败原因是连接关闭，建议启用自动重连机制")
        elif main_reason == "network_timeout":
            recommendations.append("主要失败原因是网络超时，建议优化网络配置或增加超时时间")
        elif main_reason == "send_failed":
            recommendations.append("主要失败原因是发送失败，建议检查WebSocket连接状态")
    
    # 基于性能分析的建议
    performance_grade = performance_analysis.get("performance_grade", "")
    if performance_grade in ["一般", "较差"]:
        recommendations.append("心跳响应时间较长，建议使用更快的网络节点")
        recommendations.append("考虑启用多节点智能切换以提升性能")
    
    # 基于成功率的建议
    success_rate = metrics.get("success_rate", 100)
    if success_rate < 90:
        recommendations.append("成功率过低，建议全面检查网络连接和服务器状态")
    
    return recommendations
