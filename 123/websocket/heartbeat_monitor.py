"""
🔥 统一心跳监控器 - 第32个核心统一模块
解决心跳监控分散、缺乏统一状态查询、失败原因分析不足的问题

职责：
1. 统一管理所有交易所的心跳状态
2. 提供详细的心跳失败分析和分类
3. 统一的心跳监控状态查询接口
4. 心跳性能统计和趋势分析
5. 智能心跳失败预警和恢复建议

设计原则：
- 遵循31个核心统一模块的设计标准
- 单例模式，全局唯一实例
- 实时状态监控和历史数据分析
- 详细的失败原因分类和解决方案
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict

from utils.logger import get_logger


class HeartbeatStatus(Enum):
    """心跳状态枚举"""
    HEALTHY = "healthy"          # 健康
    WARNING = "warning"          # 警告（偶发失败）
    CRITICAL = "critical"        # 严重（连续失败）
    FAILED = "failed"           # 失败（长时间无响应）
    UNKNOWN = "unknown"         # 未知（未初始化）


class HeartbeatFailureReason(Enum):
    """心跳失败原因枚举"""
    CONNECTION_CLOSED = "connection_closed"      # 连接已关闭
    NETWORK_TIMEOUT = "network_timeout"          # 网络超时
    SEND_FAILED = "send_failed"                 # 发送失败
    RESPONSE_TIMEOUT = "response_timeout"        # 响应超时
    PROTOCOL_ERROR = "protocol_error"           # 协议错误
    UNKNOWN_ERROR = "unknown_error"             # 未知错误


@dataclass
class HeartbeatMetrics:
    """心跳指标"""
    total_sent: int = 0                    # 总发送次数
    total_success: int = 0                 # 总成功次数
    total_failed: int = 0                  # 总失败次数
    consecutive_failures: int = 0          # 连续失败次数
    last_success_time: float = 0.0         # 最后成功时间
    last_failure_time: float = 0.0         # 最后失败时间
    avg_response_time: float = 0.0         # 平均响应时间
    max_response_time: float = 0.0         # 最大响应时间
    min_response_time: float = float('inf') # 最小响应时间
    success_rate: float = 100.0            # 成功率
    
    # 最近的响应时间历史（最多保留100个）
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    
    # 失败原因统计
    failure_reasons: Dict[HeartbeatFailureReason, int] = field(default_factory=lambda: defaultdict(int))


@dataclass
class HeartbeatEvent:
    """心跳事件"""
    exchange: str
    timestamp: float
    success: bool
    response_time_ms: float
    failure_reason: Optional[HeartbeatFailureReason] = None
    error_message: Optional[str] = None
    connection_info: Optional[Dict[str, Any]] = None


class UnifiedHeartbeatMonitor:
    """🔥 统一心跳监控器 - 第32个核心统一模块"""
    
    _instance = None
    _lock = asyncio.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化统一心跳监控器"""
        if hasattr(self, '_initialized'):
            return
            
        self.logger = get_logger(self.__class__.__name__)
        
        # 心跳状态存储：exchange -> HeartbeatMetrics
        self.heartbeat_metrics: Dict[str, HeartbeatMetrics] = {}
        
        # 心跳事件历史（最多保留1000个事件）
        self.heartbeat_events: deque = deque(maxlen=1000)
        
        # 当前心跳状态：exchange -> HeartbeatStatus
        self.current_status: Dict[str, HeartbeatStatus] = {}
        
        # 监控配置
        self.warning_threshold = 3      # 连续失败3次进入警告状态
        self.critical_threshold = 5     # 连续失败5次进入严重状态
        self.failed_threshold = 10      # 连续失败10次进入失败状态
        self.timeout_threshold = 60.0   # 60秒无心跳视为失败
        
        # 监控任务
        self.monitor_task: Optional[asyncio.Task] = None
        self.running = False
        
        self._initialized = True
        self.logger.info("✅ 统一心跳监控器初始化完成")

    async def start_monitoring(self):
        """启动心跳监控"""
        if self.running:
            self.logger.warning("心跳监控已在运行")
            return
            
        self.running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        self.logger.info("✅ 统一心跳监控器启动成功")

    async def stop_monitoring(self):
        """停止心跳监控"""
        self.running = False
        if self.monitor_task and not self.monitor_task.done():
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        self.logger.info("✅ 统一心跳监控器已停止")

    def record_heartbeat_event(self, exchange: str, success: bool, response_time_ms: float,
                              failure_reason: Optional[HeartbeatFailureReason] = None,
                              error_message: Optional[str] = None,
                              connection_info: Optional[Dict[str, Any]] = None):
        """🔥 记录心跳事件"""
        try:
            # 初始化交易所指标
            if exchange not in self.heartbeat_metrics:
                self.heartbeat_metrics[exchange] = HeartbeatMetrics()
                self.current_status[exchange] = HeartbeatStatus.UNKNOWN

            metrics = self.heartbeat_metrics[exchange]
            current_time = time.time()

            # 更新基础统计
            metrics.total_sent += 1
            
            if success:
                metrics.total_success += 1
                metrics.consecutive_failures = 0  # 重置连续失败计数
                metrics.last_success_time = current_time
                
                # 更新响应时间统计
                metrics.response_times.append(response_time_ms)
                if response_time_ms < metrics.min_response_time:
                    metrics.min_response_time = response_time_ms
                if response_time_ms > metrics.max_response_time:
                    metrics.max_response_time = response_time_ms
                
                # 计算平均响应时间
                if metrics.response_times:
                    metrics.avg_response_time = sum(metrics.response_times) / len(metrics.response_times)
                
            else:
                metrics.total_failed += 1
                metrics.consecutive_failures += 1
                metrics.last_failure_time = current_time
                
                # 记录失败原因
                if failure_reason:
                    metrics.failure_reasons[failure_reason] += 1

            # 计算成功率
            if metrics.total_sent > 0:
                metrics.success_rate = (metrics.total_success / metrics.total_sent) * 100

            # 更新状态
            self._update_heartbeat_status(exchange)

            # 记录事件
            event = HeartbeatEvent(
                exchange=exchange,
                timestamp=current_time,
                success=success,
                response_time_ms=response_time_ms,
                failure_reason=failure_reason,
                error_message=error_message,
                connection_info=connection_info
            )
            self.heartbeat_events.append(event)

            # 记录详细日志
            self._log_heartbeat_event(event)

        except Exception as e:
            self.logger.error(f"记录心跳事件失败: {e}")

    def _update_heartbeat_status(self, exchange: str):
        """🔥 更新心跳状态"""
        metrics = self.heartbeat_metrics[exchange]
        current_time = time.time()
        
        # 检查是否长时间无心跳
        time_since_last = current_time - max(metrics.last_success_time, metrics.last_failure_time)
        if time_since_last > self.timeout_threshold:
            self.current_status[exchange] = HeartbeatStatus.FAILED
            return

        # 根据连续失败次数确定状态
        if metrics.consecutive_failures == 0:
            self.current_status[exchange] = HeartbeatStatus.HEALTHY
        elif metrics.consecutive_failures < self.warning_threshold:
            self.current_status[exchange] = HeartbeatStatus.HEALTHY
        elif metrics.consecutive_failures < self.critical_threshold:
            self.current_status[exchange] = HeartbeatStatus.WARNING
        elif metrics.consecutive_failures < self.failed_threshold:
            self.current_status[exchange] = HeartbeatStatus.CRITICAL
        else:
            self.current_status[exchange] = HeartbeatStatus.FAILED

    def _log_heartbeat_event(self, event: HeartbeatEvent):
        """🔥 记录心跳事件日志"""
        try:
            status = self.current_status.get(event.exchange, HeartbeatStatus.UNKNOWN)
            metrics = self.heartbeat_metrics.get(event.exchange)
            
            if event.success:
                self.logger.info(f"💓 [心跳监控] {event.exchange} | "
                               f"状态=成功 | "
                               f"响应时间={event.response_time_ms:.1f}ms | "
                               f"成功率={metrics.success_rate:.1f}% | "
                               f"连续成功={metrics.total_success - metrics.total_failed + metrics.consecutive_failures} | "
                               f"整体状态={status.value}")
            else:
                self.logger.warning(f"💔 [心跳监控] {event.exchange} | "
                                  f"状态=失败 | "
                                  f"失败原因={event.failure_reason.value if event.failure_reason else '未知'} | "
                                  f"错误信息={event.error_message[:50] if event.error_message else '无'} | "
                                  f"连续失败={metrics.consecutive_failures} | "
                                  f"成功率={metrics.success_rate:.1f}% | "
                                  f"整体状态={status.value}")
        except Exception as e:
            self.logger.debug(f"心跳事件日志记录失败: {e}")

    async def _monitor_loop(self):
        """🔥 监控循环"""
        while self.running:
            try:
                await asyncio.sleep(10)  # 每10秒检查一次
                
                current_time = time.time()
                for exchange in list(self.heartbeat_metrics.keys()):
                    metrics = self.heartbeat_metrics[exchange]
                    
                    # 检查长时间无心跳的情况
                    last_activity = max(metrics.last_success_time, metrics.last_failure_time)
                    if last_activity > 0 and current_time - last_activity > self.timeout_threshold:
                        old_status = self.current_status.get(exchange, HeartbeatStatus.UNKNOWN)
                        self.current_status[exchange] = HeartbeatStatus.FAILED
                        
                        if old_status != HeartbeatStatus.FAILED:
                            self.logger.error(f"🚨 [心跳监控] {exchange} 长时间无心跳响应 | "
                                            f"距离最后活动={current_time - last_activity:.1f}秒 | "
                                            f"状态变更={old_status.value} -> {HeartbeatStatus.FAILED.value}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"心跳监控循环异常: {e}")

    def get_heartbeat_status(self, exchange: Optional[str] = None) -> Dict[str, Any]:
        """🔥 获取心跳状态"""
        if exchange:
            # 获取单个交易所状态
            if exchange not in self.heartbeat_metrics:
                return {"exchange": exchange, "status": "not_monitored"}
            
            metrics = self.heartbeat_metrics[exchange]
            status = self.current_status[exchange]
            
            return {
                "exchange": exchange,
                "status": status.value,
                "metrics": {
                    "total_sent": metrics.total_sent,
                    "total_success": metrics.total_success,
                    "total_failed": metrics.total_failed,
                    "consecutive_failures": metrics.consecutive_failures,
                    "success_rate": metrics.success_rate,
                    "avg_response_time": metrics.avg_response_time,
                    "max_response_time": metrics.max_response_time,
                    "min_response_time": metrics.min_response_time if metrics.min_response_time != float('inf') else 0,
                    "last_success_time": metrics.last_success_time,
                    "last_failure_time": metrics.last_failure_time,
                    "failure_reasons": {reason.value: count for reason, count in metrics.failure_reasons.items()}
                }
            }
        else:
            # 获取所有交易所状态
            result = {
                "summary": {
                    "total_exchanges": len(self.heartbeat_metrics),
                    "healthy_count": sum(1 for status in self.current_status.values() if status == HeartbeatStatus.HEALTHY),
                    "warning_count": sum(1 for status in self.current_status.values() if status == HeartbeatStatus.WARNING),
                    "critical_count": sum(1 for status in self.current_status.values() if status == HeartbeatStatus.CRITICAL),
                    "failed_count": sum(1 for status in self.current_status.values() if status == HeartbeatStatus.FAILED),
                },
                "exchanges": {}
            }
            
            for exchange in self.heartbeat_metrics:
                result["exchanges"][exchange] = self.get_heartbeat_status(exchange)
            
            return result


# 全局实例
_heartbeat_monitor = None

def get_heartbeat_monitor() -> UnifiedHeartbeatMonitor:
    """获取统一心跳监控器单例"""
    global _heartbeat_monitor
    if _heartbeat_monitor is None:
        _heartbeat_monitor = UnifiedHeartbeatMonitor()
    return _heartbeat_monitor
